---
title: 南泥湾
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 南泥湾

Base URLs:

# Authentication

# 班组日考核管理

## POST 导出考核表

POST /standardgroup/daily-assessment/export

导出考核表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*REST消息*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|

# 数据模型

