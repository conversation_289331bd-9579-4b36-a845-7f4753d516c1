<template>
  <div class="group-daily-assessment">
    <Row :gutter="8">
      <Col span="6">
        <ContentCard>
          <div class="assessment-left">
            <Tree :data="assessmentTree" @on-select-change="changeAssessment"></Tree>
          </div>
        </ContentCard>
      </Col>
      <Col span="18">
        <ContentCard>
          <BaseForm
            :model="searchForm"
            :label-width="90"
            :init-form-data="true"
            @handleSubmit="handleSearch"
            @handleReset="handleReset"
          >
            <template #formitem>
              <FormItem label="上传人" prop="uploaderName">
                <Input
                  v-model="searchForm.uploaderName"
                  placeholder="请输入上传人姓名"
                  clearable
                ></Input>
              </FormItem>
            </template>
          </BaseForm>
          <div class="action-box">
            <Button type="primary" @click="handleExport">导出</Button>
          </div>
          <!-- 数据表格 -->
          <EmpTable
            :columns="columns"
            url="/standardgroup/daily-assessment/listPageWithQuery"
            ref="table"
            @update:changePage="()=>{selectedList=[]}"
            @update:pageSize="()=>{selectedList=[]}"
            @on-selection-change="getSelectionChange"
            @on-sort-change="handleSortChange"
          >
            <template slot="assessmentResult" slot-scope="{ row }">
              <span :class="getConclusionClass(row.totalScore)">{{ row.assessmentResult }}</span>
            </template>
            <template slot="assessmentTable" slot-scope="{ row }">
              <linkBtn @click="handleViewAssessment(row)">小立法{{ formatDateChinese(row.assessmentDate) }}</linkBtn>
            </template>

          </EmpTable>
        </ContentCard>
      </Col>
    </Row>
    
    <!-- 考核表查看弹窗 -->
    <assessmentModal ref="assessmentModal"></assessmentModal>
  </div>
</template>

<script>
import Util from '@/libs/util'
import assessmentModal from './components/assessmentModal.vue'

const defaultSearch = {
  uploaderName: ''
};

export default {
  name: 'GroupDailyAssessment',
  components: {
    assessmentModal
  },
  data() {
    return {
      assessmentTree: [],
      searchForm: Util.objClone(defaultSearch),
      columns: [
        { type: "selection", width: 40, align: "center" },
        { title: "考核日期", key: "assessmentDate", tooltip: true, sortable: 'custom' },
        { title: "考核分数", key: "totalScore", tooltip: true, sortable: 'custom' },
        { title: "考核结论", slot: "assessmentResult", tooltip: true },
        { title: "考核表", slot: "assessmentTable", tooltip: true },
        { title: "上传人", key: "uploaderIdI18n", tooltip: true },
        { title: "上传时间", key: "uploadTime", tooltip: true },
      ],
      selectedList: [],
    };
  },
  mounted() {
    this.getAssessmentTree()
    this.handleSearch()
  },
  methods: {
    // 排序变化处理
    handleSortChange(column, key, order) {
      this.searchForm.sortField = column.key
      this.searchForm.sortOrder = order
      this.handleSearch()
    },
    
    // 搜索
    handleSearch() {
      this.$refs.table.search(this.searchForm);
    },
    
    // 重置搜索
    handleReset() {
      this.searchForm = Util.objClone(defaultSearch);
      this.handleSearch();
    },
    
    // 查看考核表
    handleViewAssessment(row) {
      // 调用接口查询考核表详情
      this.$Util.request(`/standardgroup/daily-assessment/${row.id}`, {}, 'get').then(res => {
        if (res.data && res.data.success) {
          // 将详情数据传递给弹窗组件
          this.$refs.assessmentModal.show(res.data.data)
        } else {
          this.$Message.error(res.data.message || '获取考核表详情失败')
        }
      })
    },
    
    // 导出考核表
    handleExport() {
      if (this.selectedList.length === 0) {
        this.$Message.warning('请选择要导出的数据');
        return;
      }
      let ids = this.selectedList.map(k => k.id).join(',')
      const windowToken = Util.getWindowToken()
      let name = localStorage.getItem(windowToken + 'projectName')
      name += '班组日考核表-'
      name += this.$Util.formatDate(new Date(), "yyyy-MM-dd") + '.zip'
      this.$Util.exportData('/standardgroup/daily-assessment/export?ids=' + ids, {}, name, 'post')
    },
    

    
    // 选择变化处理
    getSelectionChange(rows) {
      this.selectedList = rows;
    },
    
    // 选择考核项
    changeAssessment(arr, row) {
      // 先清除所有节点的选中状态
      this.clearAllSelected(this.assessmentTree);
      // 设置当前节点为选中状态
      row.selected = true;
      
      if (row.value === 'all') {
        this.searchForm.assessmentMonth = ''
      } else {
        // 使用节点值作为筛选条件
        this.searchForm.assessmentMonth = row.value
      }
      this.handleSearch()
    },
    
    // 获取考核树形结构
    getAssessmentTree() {
      this.$Util.request('/standardgroup/daily-assessment/assessment-tree', {}, 'get').then(res => {
        if (res.data && res.data.success) {
          // 处理接口返回的树形结构数据
          const processTreeData = (node) => {
            return {
              value: node.id,
              title: node.name,
              expand: node.type === 'root',
              selected: node.selected || false,
              children: node.children ? node.children.map(child => processTreeData(child)) : []
            }
          }
          
          this.assessmentTree = [processTreeData(res.data.data)]
        }
      })
    },
    
    // 清除所有节点的选中状态
    clearAllSelected(nodes) {
      nodes.forEach(node => {
        node.selected = false;
        if (node.children && node.children.length > 0) {
          this.clearAllSelected(node.children);
        }
      });
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString()
    },

    // 格式化日期为中文格式
    formatDateChinese(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = d.getMonth() + 1
      const day = d.getDate()
      return `${year}年${month}月${day}日`
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString()
    },
    
    // 获取分数样式类
    getScoreClass(score) {
      if (!score) return ''
      const numScore = Number(score)
      if (numScore < 60) return 'score-fail'
      if (numScore < 80) return 'score-pass'
      return 'score-excellent'
    },
    
    // 获取结论样式类
    getConclusionClass(score) {
      if (!score) return ''
      const numScore = Number(score)
      if (numScore < 60) return 'conclusion-fail'
      if (numScore < 80) return 'conclusion-pass'
      return 'conclusion-excellent'
    }
  }
};
</script>

<style lang="less" scoped>
.group-daily-assessment {
  .content-card{
    height: 100%;
    /deep/ .ivu-card-body{
      height: 100%;
    }
  }
  .assessment-left {
    height: 100%;
    overflow-y: auto;
  }
  .action-box {
    margin-bottom: 8px;
    display: flex;
    justify-content: flex-end;
  }
  .score-text {
    font-weight: bold;
    color: #1890ff;
  }
  .score-fail {
    font-weight: bold;
    color: #f5222d;
  }
  .score-pass {
    font-weight: bold;
    color: #fa8c16;
  }
  .score-excellent {
    font-weight: bold;
    color: #52c41a;
  }
  .conclusion-fail {
    color: #f5222d;
  }
  .conclusion-pass {
    color: #fa8c16;
  }
  .conclusion-excellent {
    color: #52c41a;
  }
}
</style> 