<template>
  <Modal title="小立法考核表" v-model="visible" :footer-hide="true" width="75%">
    <div v-if="detailObj && detailObj.id" class="scroll-box">
      <div class="title-box">
        桥梁一工班标准化建设"小立法"
      </div>
      <div class="info-box">
        <div class="info-item">工程名称: {{ detailObj.projectName || '-' }}</div>
      </div>
      <div class="table-main">
        <table>
          <tr>
            <th style="width: 50px">序号</th>
            <th style="width: 100px">考核大类</th>
            <th>考核内容</th>
            <th style="width: 60px">分值1</th>
            <th style="width: 60px">分值2</th>
            <th style="width: 80px">得分</th>
            <th style="width: 150px">考核结果</th>
            <th style="width: 100px">被考核人员</th>
          </tr>
          <tr v-for="(item, index) in groupedItems" :key="index">
            <td>{{ index + 1 }}</td>
            <td v-if="item.isFirstInType" :rowspan="item.typeCount">
              {{ item.itemType }}
            </td>
            <td style="text-align: left;">{{ item.typeIndex }}. {{ item.itemContent }}</td>
            <td>{{ getScoreUnit(item) }}</td>
            <td v-if="item.itemType === '否决项' && item.isFirstInType"
                :rowspan="item.typeCount"
                class="veto-cell">
              {{ getVetoItemValue(item) }}
            </td>
            <td v-else-if="item.itemType !== '否决项'">{{ item.scoreValue || '-' }}</td>
            <td>{{ getItemScore(item) }}</td>
            <td v-if="index === 0" :rowspan="groupedItems.length + 1">
              <div class="score-section">
                <div class="score-group">(1) 95<分值≤100</div>
                <div>判定：优秀<span class="checkbox">{{ getScoreResult() === '优秀' ? '☑' : '☐' }}</span></div>
                <div>班组长：25元/班</div>
                <div>作业人员：15元/班</div>
                <div class="score-group">(2) 90<分值≤95</div>
                <div>判定：合格<span class="checkbox">{{ getScoreResult() === '合格' ? '☑' : '☐' }}</span></div>
                <div>班组长：20元/班</div>
                <div>作业人员：10元/班</div>
                <div class="score-group">(3) 0<分值≤90</div>
                <div>判定：不合格<span class="checkbox">{{ getScoreResult() === '不合格' ? '☑' : '☐' }}</span></div>
                <div>按照合格标准处罚湖北科贝建设工程有限公司(标准一工班)</div>
              </div>
            </td>
            <td v-if="index === 0" :rowspan="groupedItems.length + 1">
              <div class="personnel-list">
                <div v-for="(name, nameIndex) in detailObj.assessedPersonNames" :key="nameIndex">
                  {{ name }}
                </div>
              </div>
            </td>
          </tr>
          <tr>
            <td colspan="5" style="text-align: center;">应得分</td>
            <td>{{ detailObj.totalScore }}</td>
            <td colspan="2"></td>
          </tr>
        </table>
      </div>
      <div class="footer-info">
        <div class="supervisor-info">
          <div class="signBox">
            <span class="signItem">
              <span>进驻督导人员：</span>
              <img
                v-if="detailObj.supervisorSignature"
                class="signImg"
                :src="detailObj.supervisorSignature"
              />
            </span>
          </div>
          <span class="date-info">日期：{{ formatDateChinese(detailObj.assessmentDate) }}</span>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'AssessmentModal',
  data() {
    return {
      visible: false,
      detailObj: {}
    }
  },
  computed: {
    // 分组处理考核项数据
    groupedItems() {
      if (!this.detailObj.assessmentItems) return []

      const items = [...this.detailObj.assessmentItems].sort((a, b) => a.sortOrder - b.sortOrder)
      const typeCount = {}

      // 统计每个类型的数量
      items.forEach(item => {
        typeCount[item.itemType] = (typeCount[item.itemType] || 0) + 1
      })

      let currentType = ''
      let typeIndex = 0
      return items.map(item => {
        const isFirstInType = currentType !== item.itemType
        if (isFirstInType) {
          currentType = item.itemType
          typeIndex = 1
        } else {
          typeIndex++
        }

        return {
          ...item,
          isFirstInType,
          typeCount: typeCount[item.itemType],
          typeIndex: typeIndex
        }
      })
    }
  },
  methods: {
    // 显示弹窗
    show(data) {
      this.detailObj = data
      this.visible = true
    },

    // 关闭弹窗
    close() {
      this.visible = false
      this.detailObj = {}
    },

    // 获取分值单位
    getScoreUnit(item) {
      if (item.itemType === '减分项') {
        return '每人次'
      }
      if (item.itemType === '加分项') {
        return '每次'
      }
      if (item.itemType === '否决项') {
        return '每项'
      }
      return '每次'
    },

    // 获取分值显示格式
    getScoreValue(item) {
      if (item.itemType === '减分项') {
        return `每人次/-${Math.abs(item.scoreValue || 0)}`
      }
      if (item.itemType === '加分项') {
        return `每次/+${item.scoreValue || 0}`
      }
      if (item.itemType === '否决项') {
        return '每项/当天取消考核，判定为不合格'
      }
      return item.scoreValue || '-'
    },

    // 获取考核项得分
    getItemScore(item) {
      // 根据接口数据结构，减分项显示负数
      if (item.itemType === '减分项' && item.deductScore) {
        return `${item.deductScore}`
      }
      // 加分项显示正数
      if (item.itemType === '加分项' && item.bonusScore) {
        return `${item.bonusScore}`
      }
      // 否决项显示是否
      if (item.itemType === '否决项') {
        return item.isVeto ? '不合格' : ''
      }
      // 其他情况如果是0则不显示，否则显示实际值
      const score = item.deductScore || item.bonusScore || 0
      return score === 0 ? '' : score
    },

    // 格式化日期为中文格式
    formatDateChinese(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = d.getMonth() + 1
      const day = d.getDate()
      return `${year}年${month}月${day}日`
    },

    // 根据总分判断考核结果
    getScoreResult() {
      const totalScore = this.detailObj.totalScore || 0
      if (totalScore > 95) {
        return '优秀'
      } else if (totalScore > 90) {
        return '合格'
      } else {
        return '不合格'
      }
    },

    // 获取否决项的值（取第一项的scoreValue）
    getVetoItemValue(item) {
      if (item.itemType === '否决项') {
        // 找到同类型的第一项
        const vetoItems = (this.detailObj.assessmentItems && this.detailObj.assessmentItems.filter(i => i.itemType === '否决项')) || []
        const firstVetoItem = vetoItems[0]
        return (firstVetoItem && firstVetoItem.scoreValue) || '当天取消考核，判定为不合格。'
      }
      return item.scoreValue || '-'
    },

    // 获取否决项的值（取第一项的scoreValue）
    getVetoItemValue(item) {
      if (item.itemType === '否决项') {
        // 找到同类型的第一项
        const vetoItems = (this.detailObj.assessmentItems && this.detailObj.assessmentItems.filter(i => i.itemType === '否决项')) || []
        const firstVetoItem = vetoItems[0]
        return (firstVetoItem && firstVetoItem.scoreValue) || '当天取消考核，判定为不合格。'
      }
      return item.scoreValue || '-'
    }
  }
}
</script>

<style lang="less" scoped>
@borderColor: #dcdee2;

.title-box {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.info-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.table-main {
  width: 100%;
  table {
    width: 100%;
    border: 1px solid @borderColor;
    border-collapse: collapse;
    th,
    td {
      border-bottom: 1px solid @borderColor;
      border-right: 1px solid @borderColor;
      padding: 8px;
      text-align: center;
      vertical-align: middle;
    }
    th {
      background-color: #f8f8f9;
      font-weight: bold;
    }
  }
}

.score-section {
  text-align: left;
  font-size: 12px;
  line-height: 1.4;
  div {
    margin-bottom: 2px;
  }
  .score-group {
    margin-top: 8px;
    &:first-child {
      margin-top: 0;
    }
  }
  .checkbox {
    font-size: 18px;
    margin-left: 4px;
    font-family: Arial, sans-serif;
  }
}

.personnel-list {
  text-align: left;
  div {
    margin-bottom: 4px;
  }
}

.veto-cell {
  width: 60px !important;
  max-width: 60px;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  text-align: center;
  vertical-align: middle;
}

.signBox {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  .signItem {
    display: flex;
    align-items: center;

    .signImg {
      width: 100px;
      margin-left: 10px;
    }
  }
}

.footer-info {
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .supervisor-info {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .date-info {
      margin-left: auto;
    }
  }
}
</style>