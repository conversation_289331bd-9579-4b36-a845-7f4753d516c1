<template>
  <div>
    <div class="box">
      <div class="box-item">
        设备供货单位名称：{{ deviceTypeList[0] && deviceTypeList[0].supplier }}
      </div>
      <div class="box-item">
        负责人：{{ deviceTypeList[0] && deviceTypeList[0].responsiblePerson }}
      </div>
      <div class="box-item">
        设备入场时间：{{ deviceTypeList[0] && deviceTypeList[0].installTime }}
      </div>
      <div class="box-item">
        设备采集频率：{{ deviceTypeList[0] && deviceTypeList[0].collectFreq }}
      </div>
    </div>
    <div class="box">
      <div class="card">
        <div class="card-item" v-for="(item, index) in deviceTypeList" :key="index">
          <div class="item">
            <span>{{ item.name }}</span>
            <span class="flex">设备状态 &nbsp;&nbsp;<color :class="'online'"><span
                  :style="{ color: item.onlineState == 0 ? '#999' : '#78CD82' }">{{ item.onlineState == 0 ? '离线' :
                  '在线'}}</span><span :style="{ color: item.onlineState == 0 ? '#78CD82' : 'res' }"></span><span
                  :style="{ color: item.status == 0 ? '#4E9646' : '#FF0000' }">{{ item.status == 0 ? '正常' :
                  '告警'}}</span></color></span>
            <span>更新时间： {{ item.modifyTime }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="box">
      <div class="titles">
        <span>设备数据分析</span>
        <div class="btns">
          设备 <Select placeholder="请选择" v-model="activeItem" style="width: 300px;" @on-change="changeDeviceType">
            <Option v-for="(item, index) in deviceTypeList" :key="index" :value="item">
              {{ item.name }}
            </Option>
          </Select>
        </div>
      </div>
    </div>
    <div class="box">
      <div class="echartsContainer">
        <div class="echarts-item">
          <div class="echarts-title">
            <div>电缆温度曲线</div>
            <div class="flex"><span @click="getLists(0)" style="cursor: pointer;">近24小时</span><span @click="getLists(1)" style="cursor: pointer;">近一月</span></div>
          </div>
          <div class="echarts1">
            <div id="echarts1" style="width: 100%; height: 100%;" v-if="activeItem"></div>
            <div v-else style="width: 100%; text-align: center;">暂无数据</div>
          </div>
        </div>
        <div class="echarts-item">
          <div class="echarts-title">
            <div>漏电流曲线</div>
            <div class="flex"><span @click="getList(0)" style="cursor: pointer;">近24小时</span><span @click="getList(1)" style="cursor: pointer;">近一月</span></div>
          </div>
          <div class="echarts2">
            <div id="echarts2" style="width: 100%; height: 100%;" v-if="activeItem"></div>
            <div v-else style="width: 100%; text-align: center;">暂无数据</div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import * as echarts from 'echarts';
import Util from '@/libs/util';
export default {
  name: "fire",
  props: ['deviceTypeList'],
  data() {
    return {
      myCharts1: null,
      myCharts2: null,
      activeItem: {},
      echartsData: {},
      echartsData1: {},
      lineColor: ['#79FA83', '#F8CF47', '#76F1FD', '#E3A688']
    }
  },
  mounted() {
    setTimeout(() => {
      this.changeDeviceType(this.deviceTypeList[0]);
    }, 500);
  },
  methods: {
    changeDeviceType(val) {
      this.activeItem = val;
      this.getList(0);
      this.getLists(0);
    },
    getLists(time) {
      // 先获取设备属性配置
      Util.requestPost('/deviceAttributeStatus/getProps', {
        deviceCode: this.activeItem.code
      }).then(attrRes => {
        const attrList = attrRes.data.data || [];
        
        // 然后获取温度数据
        Util.requestPost('/electricBoxBi/temperatureLineHursOrMonthly', {
          deviceCode: this.activeItem.code,
          dailyOrMonthly: time
        }).then(res => {
          this.echartsData1 = res.data.data;
          this.echartsData1.echarts1 = [];
          
          // 动态组装数据
          for(let key in this.echartsData1) {
            // 查找对应的属性配置
            const attrItem = attrList.find(item => {
              // 处理不同的属性名格式
              const propCode = item.propCode;
              const keyName = key.replace('_array', '');
              
              // 匹配规则：temperature_1_array -> temperature_1
              if (keyName === propCode) {
                return true;
              }
              
              // 如果直接匹配失败，尝试其他匹配方式
              if (key === propCode + '_array') {
                return true;
              }
              
              return false;
            });
            
            if (attrItem) {
              this.echartsData1.echarts1.push({
                title: attrItem.propName,
                color: this.getColorByIndex(this.echartsData1.echarts1.length),
                echartsData: this.echartsData1[key]
              });
            }
          }
          
          // 初始化图表
          this.initCharts1();
        });
      });
    },
    getList(time) {
      Util.requestPost('/electricBoxBi/boxRecordByhoursOrMonthly', {
        deviceCode: this.activeItem.code,
        electricBoxId: this.activeItem.id,
        dailyOrMonthly: time
      }).then(res => {
        this.echartsData = res.data.data;
        // 漏电
        this.initCharts2();
      })
    },
    charts1Server(echartsData) {
      return echartsData.map((i, v) => {
        return {
            name: i.title,
            data: i.echartsData,
            type: 'line',
            lineStyle: {
              color: i.color,
            },
            itemStyle: {
              color: i.color,
            },
            markLine: {
              data: [
                {
                  yAxis: 1000,      // 固定值预警线
                  lineStyle: {
                    type: 'dashed', // 虚线
                    color: '#ff0000' // 红色
                  },
                  label: {
                    show: true,
                    position: 'middle',
                    formatter: '温度预警线',
                    color: '#fff',
                    fontSize: 10
                  }
                }
              ]
            }
          }
      })
    },
    initCharts1() {
      this.myCharts1 && this.myCharts1.dispose();
      document.getElementById('echarts1').style.width = document.querySelector('.echarts1').offsetWidth + 'px';
      document.getElementById('echarts1').style.height = document.querySelector('.echarts1').offsetHeight + 'px';
      this.myCharts1 = echarts.init(document.getElementById('echarts1'));
      const option = {
        grid: {
          top: '15%',
          left: '8%',
          right: '0%',
          bottom: '10%'
        },
        legend: {
          data: this.echartsData1.echarts1.map(i => i.title),
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        xAxis: {
          type: 'category',
          data: this.echartsData1.abscissa
        },
        yAxis: {
          name: '单位' + this.echartsData1.tempUnit ,
          type: 'value'
        },
        series: this.charts1Server(this.echartsData1.echarts1)
      }
      this.myCharts1.setOption(option);
    },
    initCharts2() {
      this.myCharts2 && this.myCharts2.dispose();
      document.getElementById('echarts2').style.width = document.querySelector('.echarts2').offsetWidth + 'px';
      document.getElementById('echarts2').style.height = document.querySelector('.echarts2').offsetHeight + 'px';
      this.myCharts2 = echarts.init(document.getElementById('echarts2'));
      const option = {
        grid: {
          top: '15%',
          left: '8%',
          right: '0%',
          bottom: '10%'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        xAxis: {
          type: 'category',
          data: this.echartsData.abscissa
        },
        yAxis: {
          name: '单位' + this.echartsData.electricUnit,
          type: 'value'
        },
        series: [
          {
            data: this.echartsData.residualCurrentArray,
            type: 'line',
            smooth: true,
            itemStyle: {
              color: '#79FA83',
              opacity: 0,
            },
            emphasis: {
              itemStyle: {
                opacity: 1,
                color: {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [{
                    offset: .2, color: 'rgba(255, 255, 255)' // 0% 处的颜色
                  }, {
                    offset: 0.5, color: 'rgba(36, 76, 173)' // 100% 处的颜色
                  }, {
                    offset: 1, color: 'rgba(36, 76, 173, 0.1)' // 100% 处的颜色
                  }],
                  global: false // 缺省为 false
                },
                borderColor: '#95B6F9',
                borderWidth: 1
              },
              scale: 3,
            }
          }
        ]
      }
      this.myCharts2.setOption(option);
    },
    // 根据索引获取颜色
    getColorByIndex(index) {
      return this.lineColor[index % this.lineColor.length];
    }
  }
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  margin: 10px 0;
  padding: 10px;

  .box-item {
    width: 19%;
  }

  .card {
    width: 100%;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    .card-item {
      width: 24%;
      background-color: #F9FBFF;
      box-sizing: border-box;
      padding: 10px;

      .item {
        width: 90%;
        height: 90%;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .flex {
          display: flex;
          gap: 10px;

          .online {
            width: 70%;
            display: flex;
            justify-content: space-between;
            align-items: center;

            span:first-child {
              color: #78CD82
            }

            span:last-child {
              color: red
            }
          }
        }
      }
    }
  }

  .echartsContainer {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: space-between;

    .echarts-item {
      width: 50%;
      height: 100%;

      .echarts1 {
        width: 90%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }

      .echarts2 {
        width: 90%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
}

.echarts-title {
  width: 90%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #6ABBE1;
    font-size: 12px;

    span {
      margin-left: 10px;
    }
  }
}

.titles {
  width: 100%;
  display: flex;
  gap: 15px;
  padding: 10px;
  background-color: #F9F9F9;
  align-items: center;

  span {
    font-size: 16px;
    font-weight: bold;
  }
}
</style>