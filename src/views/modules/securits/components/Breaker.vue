<template>
  <div>
    <div class="box">
      <div class="box-item">
        设备供货单位名称：{{ deviceTypeList[0] && deviceTypeList[0].supplier }}
      </div>
      <div class="box-item">
        负责人：{{ deviceTypeList[0] && deviceTypeList[0].responsiblePerson }}
      </div>
      <div class="box-item">
        设备入场时间：{{ deviceTypeList[0] && deviceTypeList[0].installTime }}
      </div>
      <div class="box-item">
        设备采集频率：{{ deviceTypeList[0] && deviceTypeList[0].collectFreq }}
      </div>
    </div>
    <div class="box">
      <div class="card">
        <div class="card-item" v-for="(item, index) in deviceTypeList" :key="index">
          <div class="item">
            <span>{{ item.name }}</span>
            <span class="flex">设备状态 &nbsp;&nbsp;
              <color :class="'online'">
                <span
                  :style="{ color: item.onlineState == 0 ? '#999' : '#78CD82' }">{{ item.onlineState == 0 ? '离线' :
                    '在线' }}</span>
                <span :style="{ color: item.onlineState == 0 ? '#78CD82' : 'res' }"></span>
                <span :style="{ color: item.deviceAttributeStatusList.filter(i => i.propCode == 'switch_state').length > 0 ? '#B0B9CA' : '78CD82' }">{{ item.deviceAttributeStatusList.filter(i => i.switch_state).length > 0 ? (item.deviceAttributeStatusList.filter(i => i.switch_state)[0].switch_state == 0 ? '断开' : '闭合') : ''  }}</span>
                <span :style="{ color: item.status == 0 ? '#4E9646' : '#FF0000' }">{{ item.status == 0 ? '正常' :
                  '告警' }}</span>
              </color>
            </span>
            <span>更新时间： {{ item.modifyTime }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="left">
        <h3>断路器故障统计</h3>
        <div class="total">
          <div class="num">
            总数 <span>{{ warinning.sum }}</span>
          </div>
          <div class="flex"><span>报警{{ warinning.bjSum }}</span><span>故障{{ warinning.gzSum }}</span></div>
        </div>
        <div class="echarts1">
          <div id="echarts1" style="width: 100%; height: 100%;"></div>
        </div>

      </div>
      <div class="left">
        <h3>断路器故障类型统计</h3>
        <div class="select">
          <Select placeholder="请选择" v-model="activeItem" style="width: 300px;" @on-change="changeDeviceType">
            <Option v-for="(item, index) in deviceTypeList" :key="index" :value="item">
              {{ item.name }}
            </Option>
          </Select>
          <div class="button">
            <div @click="initRight(0)">近一月</div>
            <div @click="initRight(1)">开累</div>
          </div>
        </div>
        <div class="echarts2">
          <div id="echarts2" style="width: 100%; height: 100%;"></div>
        </div>

      </div>
    </div>
  </div>

</template>

<script>
import * as echarts from 'echarts';
import Util from '@/libs/util';
export default {
  name: "Breaker",
  props: ['deviceTypeList'],
  data() {
    return {
      activeItem: {
        name: '全部',
        code: ''
      },
      myCharts1: null,
      myCharts2: null,
      echartsData2: [],
      warinning: {},
    }
  },
  mounted() {
    // this.deviceTypeList.unshift(this.activeItem);
    setTimeout(() => {
      this.changeDeviceType(this.deviceTypeList[0]);
      this.initLeft();
    }, 500);
  },
  methods: {
    changeDeviceType(item) {
      this.activeItem = item;
      this.initRight();
    },
    initLeft() {
      Util.requestGet('/circuitGivealarm/findTotalByPieChart').then(res => {
        this.warinning = res.data.data
        this.initChart1();
      })
    },
    initRight(cumulative = 0) {
      Util.requestPost('/circuitGivealarm/faultTypeByColumnar', {
        deviceCode: this.activeItem.code,
        cumulative: cumulative
      }).then(res => {
        this.echartsData2 = res.data.data
        this.initChart2();
      })
    },
    initChart2() {
      this.myCharts2 && this.myCharts2.dispose();
      document.getElementById('echarts2').style.width = document.querySelector('.echarts2').offsetWidth + 'px';
      document.getElementById('echarts2').style.height = document.querySelector('.echarts2').offsetHeight + 'px';
      this.myCharts2 = echarts.init(document.getElementById('echarts2'));
      //
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.echartsData2.abscissa,
            // 斜体
            axisLabel: {
              rotate: 45 // 设置标签旋转45度
            }
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '报警',
            type: 'bar',
            stack: 'total',  // 相同的stack值实现堆叠
            data: this.echartsData2.giveAlarm,
            itemStyle: {
              color: '#F8CF47'
            }
          },
          {
            name: '故障',
            type: 'bar',
            stack: 'total',
            data: this.echartsData2.giveFault,
            itemStyle: {
              color: 'red'
            }
          }
        ]
      }
      this.myCharts2.setOption(option)
    },
    initChart1() {
      this.myCharts1 && this.myCharts1.dispose();
      document.getElementById('echarts1').style.width = document.querySelector('.echarts1').offsetWidth + 'px';
      document.getElementById('echarts1').style.height = document.querySelector('.echarts1').offsetHeight + 'px';
      this.myCharts1 = echarts.init(document.getElementById('echarts1'));
      // 饼状图
      let option = {
        grid: {
          top: '10%',
          left: '3%',
          right: 0,
          bottom: '0%',
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.warinning.pieChartArray.map(i => {
              return {
                name: i.deviceName,
                value: i.sum
              }
            })
          }
        ]
      };
      this.myCharts1.setOption(option);
    }
  }
}
</script>

<style lang="less" scoped>
.echarts1 {
  width: 100%;
  height: 70%;
}

.echarts2 {
  width: 100%;
  height: 90%;
}

.total {
  margin: 0px auto;
  width: 80%;
  height: 80px;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #ECF2FE;

  .num {
    span {
      font-size: 16px;
    }
  }

  .flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.content {
  width: 100%;
  height: 450px;
  display: flex;
  justify-content: space-between;

  .left {
    width: 48%;
    height: 100%;
    background-color: #F9FBFF;
    box-sizing: border-box;
    padding: 10px;

    .select {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .button {
        display: flex;
        gap: 10px;

        div {
          padding: 3px 10px;
          background-color: #E1F2FE;
          cursor: pointer;
        }
      }
    }
  }
}

.circuit-breaker-stats {
  font-family: Arial, sans-serif;
  color: #333;
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

h1 {
  font-size: 24px;
  margin-bottom: 20px;
  text-align: center;
}

h3 {
  font-size: 18px;
  margin: 15px 0 10px;
}

h4 {
  font-size: 16px;
  margin: 20px 0 15px;
  color: #666;
}

.stats-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.alert-count {
  color: #f56c6c;
  font-weight: bold;
}

.fault-count h3 {
  color: #f56c6c;
}

.stats-content {
  display: flex;
  margin-top: 20px;
}

.stats-table {
  flex: 1;
  margin-right: 30px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.breaker-list {
  flex: 1;
}

.breaker-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.main-breaker {
  font-weight: bold;
  color: #409eff;
}

.breaker-name {
  flex: 2;
}

.breaker-percent {
  flex: 1;
  text-align: center;
  color: #f56c6c;
}

.breaker-value {
  flex: 1;
  text-align: right;
}

/* 特殊样式匹配图片中的布局 */
.stats-table table tr:first-child td {
  border-top: none;
}

.stats-table table tr td:first-child {
  font-weight: bold;
}

.breaker-list ul {
  list-style-type: none;
  padding-left: 0;
}

.breaker-list li:nth-child(2) .breaker-percent,
.breaker-list li:last-child .breaker-percent {
  order: -1;
  /* 百分比移到前面 */
}

.box {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  margin: 10px 0;
  padding: 10px;

  .box-item {
    width: 19%;
  }

  .card {
    width: 100%;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    .card-item {
      width: 24%;
      background-color: #F9FBFF;
      box-sizing: border-box;
      padding: 10px;

      .item {
        width: 90%;
        height: 90%;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .flex {
          display: flex;
          gap: 10px;

          .online {
            width: 70%;
            display: flex;
            justify-content: space-between;
            align-items: center;

            span:first-child {
              color: #78CD82
            }

            span:last-child {
              color: red
            }
          }
        }
      }
    }
  }

  .echartsContainer {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: space-between;

    .echarts-item {
      width: 47%;
      height: 100%;
      background-color: #F9FBFF;
      box-sizing: border-box;
      padding: 10px;

      .table1 {
        margin-top: 10px;
        width: 100%;
        height: 100%;
      }
    }
  }
}

.echarts-title {
  width: 90%;
  display: flex;
  align-items: center;
  gap: 20px;

  .flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #6ABBE1;
    font-size: 12px;

    span {
      margin-left: 10px;
    }
  }
}

.titles {
  width: 100%;
  display: flex;
  gap: 15px;
  padding: 10px;
  background-color: #F9F9F9;
  align-items: center;

  span {
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
