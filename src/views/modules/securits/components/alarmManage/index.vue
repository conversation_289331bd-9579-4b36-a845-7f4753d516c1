<template>
  <div class="page-container">
    <Row :gutter="8">
      <Col span="8">
      <Card dis-hover :bordered="false" style="">
        <Title title="告警统计"></Title>
        <div class="card-content">
          <div class="content-item" style="background: rgba(22, 93, 255, 0.12); padding: 10px 8px;">
            <img src="./images/title_pre_img.png" width="24" height="24" />
            <div style="display: flex; flex-direction: column;">
              <span>总数</span>
              <div style="display: flex; align-items: baseline; column-gap: 8px;">
                <span style="font-size: 24px;">{{ alarmStats.total }}</span>
                <span style="color: #798799;">较昨日</span>
                <img src="./images/down_img.png" width="16" height="16" v-if="alarmStats.nowSum < 0"/>
                <img src="./images/up_img.png"  v-else/>
                <span style="color: #1E2A55;">{{ alarmStats.nowSum }}</span>
              </div>
            </div>
          </div>
          <div class="content-item" style="margin-top: 8px;">
            <div style="display: flex; flex-direction: column; flex: 1;">
              <span>已处理</span>
              <span style="font-size: 24px;">{{ alarmStats.handled }}</span>
            </div>
            <div style="display: flex; flex-direction: column; flex: 1;">
              <span>未处理</span>
              <span style="font-size: 24px;">{{ alarmStats.unhandled }}</span>
            </div>
          </div>
        </div>
      </Card>
      </Col>
      <Col span="8">
      <Card dis-hover :bordered="false" style="">
        <Title title="告警等级统计"></Title>
        <Echarts ref="levelEchartsRef" :options="levelOptions" :autoresize="true" class="echart-box" />
      </Card>
      </Col>
      <Col span="8">
      <Card dis-hover :bordered="false" style="">
        <Title title="告警趋势">
          <template #right>
            <Select v-model="lineDateType" size="small" style="width:100px" @on-change="handleLineDateChange">
              <Option v-for="item in lineDateList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </template>
        </Title>
        <Echarts :options="trendOptions" :autoresize="true" class="echart-box" />
      </Card>
      </Col>
    </Row>
    <ContentCard style="min-height: calc(100vh - 342px)">
      <BaseForm :model="searchForm" :label-width="90" :init-form-data="true" @handleSubmit="handleSearch"
        @handleReset="handleReset">
        <template #formitem>
          <FormItem label="关键词" prop="name">
            <Input v-model="searchForm.uniKey" placeholder="请输入" clearable></Input>
          </FormItem>
          <FormItem label="告警类型" prop="warnState">
            <Select v-model="searchForm.alarmType" clearable>
              <Option v-for="(item, index) in alarmTypeOption" :key="index" :value="item.type">{{ item.text }}  
              </Option>
            </Select>
          </FormItem>
          <FormItem label="状态" prop="reasonState">
            <Select v-model="searchForm.status" clearable>
              <Option v-for="(item, index) in statusOption" :key="index" :value="item.key">{{ item.value }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="时间">
            <DatePicker type="daterange" placeholder="选择日期" v-model="searchForm.date" format="yyyy-MM-dd"
              @on-change="handleDateChange" clearable />
          </FormItem>
        </template>
      </BaseForm>
      <!-- 数据表格 -->
      <EmpTable :columns="columns" url="/systemAlarm/getPage" ref="table" :load-done="tableLoadDone">
        <template slot="categorySlot" slot-scope="{ row }">
          {{ row.category == 1 ? '吊车安全监测' : '--' }}
        </template>
        <template slot="levelSlot" slot-scope="{ row }">
          {{ row.level == 1 ? 'I级' : row.level == 2 ? 'II级' : row.level == 3 ? 'III级' : '--' }}
        </template>
        <template slot="statusSlot" slot-scope="{ row }">
          <Tag v-if="row.status == 1" checkable color="success">已处理</Tag>
          <Tag v-else-if="row.status == 2" checkable color="primary">误报</Tag>
          <Tag v-else checkable color="warning">未处理</Tag>
        </template>
        <template slot="actionSlot" slot-scope="{ row }">
          <LinkBtn v-if="row.status == 1 || row.status == 2" type="text" size="small" @click="handleEdit(row, true)">
            处理详情
          </LinkBtn>
          <LinkBtn v-else-if="row.settingHandleId" type="primary" size="small" @click="handleEdit(row, false)">去处理</LinkBtn>
        </template>
      </EmpTable>
    </ContentCard>

    <!-- 告警处理弹窗 -->
    <CustomModal v-model="showModal" :loading="loading" @on-ok="handleSubmit" :title="title" width="800"
      :show-default-footer="!submitObj.disabled">
      <!-- 处理详情显示模式 -->
      <div v-if="submitObj.disabled" style="padding: 20px;">
        <div style="margin-bottom: 20px;">
          <span style="font-weight: bold;">处理方式：</span>
          <span>{{ submitObj.handleRest === 0 ? '已处理' : submitObj.handleRest === 1 ? '误报' : '' }}</span>
        </div>
        <div style="margin-bottom: 20px;">
          <span style="font-weight: bold;">情况说明：</span>
          <span>{{ submitObj.handleResp || '无' }}</span>
        </div>
        <div style="margin-bottom: 20px;">
          <span style="font-weight: bold;">现场影像记录：</span>
          <div v-if="submitObj.handleFileUrls && submitObj.handleFileUrls.length > 0" style="margin-top: 8px;">
            <div v-for="(url, index) in submitObj.handleFileUrls" :key="index" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background-color: #f8f9fa; border-radius: 4px;">
              <span style="font-weight: 500;">文件 {{ index + 1 }}：</span>
              <a :href="url" target="_blank" style="color: #2d8cf0; text-decoration: underline;">
                {{ getFileName(url) }}
              </a>
            </div>
          </div>
          <span v-else>无</span>
        </div>
      </div>
      <!-- 处理编辑模式 -->
      <Form v-else ref="saveForm" :label-width="100" :model="submitObj" :rules="rules">
        <Card dis-hover>
          <FormItem label="处理方式:" prop="handleRest">
            <RadioGroup v-model="submitObj.handleRest">
              <Radio :label="0"><span>已处理</span></Radio>
              <Radio :label="1"><span>误报</span></Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="情况说明:" prop="handleResp">
            <Input v-model="submitObj.handleResp" maxlength="500" placeholder="请输入" show-word-limit type="textarea" />
          </FormItem>
          <FormItem label="现场影像记录">
            <UploadFile v-model="submitObj.handleFileUrls" :max-count="5" :multiple="false" style="width: auto;"
              :format-arr="['mp4', 'avi', 'jpeg', 'png', 'jpg']" @setResult="setResult"/>
          </FormItem>
        </Card>
      </Form>
    </CustomModal>
  </div>
</template>

<script>
import Util from '@/libs/util';
import UploadFile from './UploadFile'
const defaultSearch = {
  category: 3,
  uniKey: '',
  alarmType: '',
  status: '',
  date: []
};

import Echarts from '@/components/ECharts'
import * as echarts from 'echarts';

export default {
  components: {
    Echarts,
    UploadFile
  },
  data() {
    return {
      showModal: false,
      loading: false,
      submitObj: {
        id: '',
        handleFileUrls: [],
        handleResp: '',
        handleRest: 0,
        disabled: false
      },
      rules: {
        handleRest: [{ type: 'number', required: true, message: '请选择', trigger: 'change' }],
        handleResp: [{ type: 'string', required: true, message: '请输入', trigger: 'blur' }]
      },
      title: '',
      alarmTypeOption: [
        // { key: 7, value: '设备离线告警' },
        // { key: 8, value: '风速告警' },
        // { key: 9, value: '俯仰角告警' },
        // { key: 10, value: '力矩告警' },
        // { key: 11, value: '主钩吊重告警' },
        // { key: 12, value: '大臂长度告警' },
        // { key: 13, value: '水平度X告警' },
        // { key: 14, value: '水平度Y告警' },
        // { key: 15, value: '回转告警' },
        // { key: 16, value: '力臂告警' },
        // { key: 17, value: '安全限界告警' },
        // { key: 18, value: '作业展臂最不利工况可起重吊重告警' },
        // { key: 19, value: '最大臂长告警' },
        // { key: 20, value: '倾倒告警' },
        // { key: 21, value: '施工防侵限告警' },
        // { key: 22, value: '防倾倒侵限告警' },
        // { key: 23, value: '离开作业区域告警' }
      ],
      statusOption: [
        { key: 0, value: '未处理' },
        { key: 1, value: '已处理' },
        { key: 2, value: '误报' }
      ],
      tbHeight: 400,
      searchForm: Util.objClone(defaultSearch),
      columns: [
        { type: 'index', title: '序号', width: 60, align: 'center' },
        // { title: '设备名称', key: 'deviceName', tooltip: true},
        { title: '监测分类', key: 'categoryI18n', tooltip: true },
        { title: '监测项', key: 'testingItemI18n', tooltip: true },
        { title: '告警设备', key: 'deviceName', tooltip: true },
        { title: '告警等级', slot: 'levelSlot', tooltip: true },
        { title: '告警时间', key: 'createTime', tooltip: true },
        { title: '告警类型', key: 'alarmTypeText', tooltip: true },
        { title: '告警内容', key: 'content', tooltip: true },
        { title: '告警处理人', key: 'handleName', tooltip: true },
        { title: '状态', slot: 'statusSlot', tooltip: true },
        { title: '操作', slot: 'actionSlot', width: 80, align: 'center' }
      ],
      lineDateType: 0, // 0今日,1近一周(返回7天日期),2近一个月返回30天日期 默认今日0
      lineDateList: [
        { label: '今日', value: 0 },
        { label: '近一周', value: 1 },
        { label: '近一月', value: 2 }
      ],
      // 告警统计数据
      alarmStats: {
        total: 0,
        handled: 0,
        unhandled: 0,
        nowSum: 0
      },
      alarmTypes: [
        { label: '全部', value: '' },
        { label: '超载', value: '1' },
        { label: '超力矩', value: '2' },
        { label: '超幅', value: '3' },
        { label: '超高', value: '4' },
        { label: '超风速', value: '5' },
        { label: '碰撞', value: '6' },
        { label: '倾斜', value: '7' },
        { label: '变幅异常', value: '8' },
        { label: '回转异常', value: '9' },
        { label: '起升异常', value: '10' },
        { label: '小车异常', value: '11' },
        { label: '上限位', value: '12' },
        { label: '下限位', value: '13' },
        { label: '左限位', value: '14' },
        { label: '右限位', value: '15' },
        { label: '前限位', value: '16' },
        { label: '后限位', value: '17' },
        { label: '变幅上限位', value: '18' },
        { label: '变幅下限位', value: '19' },
        { label: '回转左限位', value: '20' },
        { label: '回转右限位', value: '21' },
        { label: '起升上限位', value: '22' },
        { label: '起升下限位', value: '23' },
        { label: '小车前限位', value: '24' },
        { label: '小车后限位', value: '25' },
        { label: '塔机上电', value: '26' },
        { label: '塔机断电', value: '27' },
        { label: '塔机上线', value: '28' },
        { label: '塔机离线', value: '29' },
        { label: '黑匣子上线', value: '30' },
        { label: '黑匣子离线', value: '31' },
        { label: '防碰撞上线', value: '32' },
        { label: '防碰撞离线', value: '33' },
        { label: '防碰撞上电', value: '34' },
        { label: '防碰撞断电', value: '35' },
        { label: '黑匣子上电', value: '36' },
        { label: '黑匣子断电', value: '37' },
        { label: '塔机故障', value: '38' },
        { label: '黑匣子故障', value: '39' },
        { label: '防碰撞故障', value: '40' },
        { label: '塔机恢复', value: '41' },
        { label: '黑匣子恢复', value: '42' },
        { label: '防碰撞恢复', value: '43' },
        { label: '其他', value: '44' }
      ],
      levelData: [],
      levelOptions: {
        tooltip: {
          trigger: 'item',
          // formatter: '{a} <br/>{b} : {c} ({d}%)'
          formatter: '{a} <br/>占比 {d}%'
        },
        legend: {
          top: '5%',
          right: '0%',
          orient: 'vertical',
          // 关键：自定义图例显示格式
          formatter: (name) => {
            // 在数据中查找匹配的条目
            const item = this.levelData.find(d => d.name === name);
            return item ? `${name}: ${item.value}` : name;
          }
        },
        color: ['#f3dd12', '#f28300', '#f30d29', '#846BCE', '#249EFF'],
        series: [
          {
            name: '报警',
            type: 'pie',
            // radius: ['40%', '70%'],
            radius: ['40%', '80%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              show: true,
              position: 'inside',
              color: '#FFF',
              formatter: (params) => {
                // 显示百分比，保留两位小数
                return `${params.percent.toFixed(2)}%`;
              }
            },
            labelLine: {
              show: false
            },
            data: []
          }
        ]
      },
      trendOptions: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: []
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '告警数量',
            type: 'line',
            stack: '总量',
            emphasis: {
              focus: 'series'
            },
            data: [],
            itemStyle: {
              color: '#5B8FF9'
            },
            lineStyle: {
              width: 2
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(91, 143, 249, 0.6)'
                },
                {
                  offset: 1,
                  color: 'rgba(91, 143, 249, 0.1)'
                }
              ])
            }
          }
        ]
      }
    };
  },
  mounted() {
    this.handleSearch();
    this.getAlarmLevelStats();
    this.getAlarmTrend();
    this.initRequest();
  },

  methods: {
    initRequest() {
      Util.request('/giveAlarmTypeConfig/selectList', {
        text: '',
        level: '',
        category: 3
      }, 'post').then(res => {
        console.log(res, 'res')
        this.alarmTypeOption = res.data.data;
      })
    },
    handleSearch() {
      this.$refs.table.search(this.searchForm);
    },
    handleReset() {
      this.searchForm = Util.objClone(defaultSearch);
      this.handleSearch();
    },
    handleDateChange(date) {
      console.log('date>>>>>>>>', date);

      if (date && date.length === 2) {
        this.searchForm.startDate = Util.formatDate(date[0], 'yyyy-MM-dd') + ' 00:00:00';
        this.searchForm.endDate = Util.formatDate(date[1], 'yyyy-MM-dd') + ' 23:59:59';
      } else {
        this.searchForm.startDate = '';
        this.searchForm.endDate = '';
      }
    },
    tableLoadDone(data) {
      return data.records;
    },
    // 获取告警等级统计/告警统计数据
    getAlarmLevelStats() {
      Util.request(`/systemAlarm/totalOldDayByLevelPieChart/3`, {}, 'get').then(res => {
        if (res.data && res.data.success) {
          this.levelData = res.data.data.levelResList.map(k => ({
            name: k.levelText,
            value: k.sum
          }));

          // Update the chart data
          this.levelOptions = {
            ...this.levelOptions,
            series: [{
              ...this.levelOptions.series[0],
              data: this.levelData
            }]
          };

          this.alarmStats = {
            total: res.data.data.sum,
            handled: res.data.data.handleSum,
            unhandled: res.data.data.unHandleSum,
            nowSum: res.data.data.nowSum
          };
        }
      });
    },
    handleLineDateChange(val) {
      console.log('>>>>>>>>', this.lineDateType);
      this.getAlarmTrend(this.lineDateType)
    },
    // 获取告警趋势数据
    getAlarmTrend(lineDateType) {
      // 这里应该调用实际的API接口获取数据
      // 示例：
      Util.request('/systemAlarm/daysWeekMonthlyByLineChart', { lineDateType: this.lineDateType, category: 3 }, 'post').then(res => {
        if (res.data && res.data.success) {
          this.trendOptions.series[0].data = res.data.data.sums;
          this.trendOptions.xAxis[0].data = res.data.data.abscissa;
        }
      })
    },
    handleEdit(row, disabled) {
      this.submitObj = {
        id: row.id,
        handleFileUrls: row.handleFileUrls ? row.handleFileUrls.split(',') : [],
        handleResp: row.handleResp ? row.handleResp : '',
        // status 0未处理，1已处理，2误报
        // handleRest 0:已处理 1:误报
        handleRest: row.status == 1 ? 0 : row.status == 2 ? 1 : -1,
        disabled: disabled
      }
      this.title = disabled ? '告警处理详情' : '告警处理'
      this.showModal = true
    },
    setResult(flag) {
      this.loading = flag;
    },
    handleSubmit() {
      this.$refs.saveForm.validate((valid) => {
        if (valid) {
          console.log(this.submitObj);
          let param = {
            ...this.submitObj,
            handleFileUrls: this.submitObj.handleFileUrls.join(','),
            category: 3
          }
          Util.request(`/systemAlarm/handle`, param, 'post').then(res => {
            if (res.data && res.data.success) {
              this.$Message.success('处理成功');
              this.showModal = false
              this.handleSearch();
            }
          })
        }
      });
    },
    // 从URL中提取文件名
    getFileName(url) {
      if (!url) return '未知文件';
      try {
        // 提取URL最后一部分作为文件名
        const parts = url.split('/');
        const fileName = parts[parts.length - 1];
        // 如果包含查询参数，只保留文件名部分
        return fileName.split('?')[0] || '未知文件';
      } catch (e) {
        return '未知文件';
      }
    }
  }
};
</script>

<style lang='less' scoped>
/deep/ .m-upload {
  width: 100% !important;
}
.page-container {
  display: flex;
  flex-direction: column;
  row-gap: 8px;

  .statistics-container {
    display: flex;
    column-gap: 8px;
    height: 350px;

    .chart-line {
      width: 100%;
      height: 350px;
    }

    .static-panel {
      display: flex;
      flex-direction: column;
      background-color: white;
      height: 100%;
      padding: 16px;

      .title-wrapper {
        display: flex;
        align-items: center;
        column-gap: 8px;

        .title {
          color: #1e554c;
          font-size: 16px;
          font-weight: 400;
        }
      }

      .content {
        display: flex;
        flex-direction: column;

        .row-item {
          display: flex;
          flex-direction: column;
          row-gap: 4px;

          .title {
            color: #1E2A55;
            font-size: 24px;
          }

          .value-wrapper {
            color: #798799;
            display: flex;
            column-gap: 4px;
            font-size: 14px;
          }
        }

        .stat-item {
          text-align: center;

          .num {
            font-size: 24px;
            font-weight: 600;
            color: #5B8FF9;
            margin-bottom: 8px;
          }

          .label {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }

    .static-right {
      background-color: white;
      flex: 1;
      border-radius: 4px;
      padding: 16px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
      }

      .chart-container {
        height: calc(100% - 32px);

        .trend-chart {
          height: 100%;
          width: 100%;
        }
      }
    }
  }

  .card-content {
    display: flex;
    flex-direction: column;
    height: 150px;

    .content-item {
      display: flex;
      column-gap: 8px;
      border-radius: 4px;
      border: 1px solid var(--, #E0E6F1);
    }
  }

  .echart-box {
    height: 150px;
    width: 100%;
  }
}
</style>
