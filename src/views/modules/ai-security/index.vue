<template>
  <div class="tab">
    <Tabs v-model="activeTab" @on-click="handleTabChange">
      <TabPane label="AI综合安防" name="AI综合安防"></TabPane>
      <TabPane label="告警设置" name="告警设置"></TabPane>
    </Tabs>
    <component :is="activeComponent"></component>
  </div>
</template>

<script>
import ai from './components/ai.vue';
import warnNum from './components/warnNum.vue';
export default {
  name: 'AIsecurity',
  components: {
    ai,
    warnNum
  },
  data() {
    return {
      activeTab: 'AI综合安防',
      activeComponent: ''
    }
  },
  mounted() {
    this.handleTabChange(this.activeTab);
  },
  methods: {
    handleTabChange(name) {
      this.activeTab = name;
      let option = {
        'AI综合安防': ai,
        '告警设置': warnNum
      }
      this.activeComponent = option[name] || null;
    }
  }
}
</script>
<style lang="less" scoped>
.tab {
  width: 100%;
  height: 40px;
}
</style>
