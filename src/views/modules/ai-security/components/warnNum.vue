<template>
  <div>
    <Card>
      <div class="top-wrapper">
        <span>数据采集频率 5s 每次</span>
        <div>告警去重时长<InputNumber :min="1" :step="1" v-model="deduplicationTime" @on-change="handleDeduplicationTime">
          </InputNumber>min</div>
      </div>
    </Card>
    <Card dis-hover>
      <Table :columns="columns" :data="tableData">
        <template #actionSlot="{ row, index }">
          <div  
            v-if="row.type != 7 && row.type != 18 && row.type != 19 && row.type != 21 && row.type != 22 && row.type != 23">
            <LinkBtn v-if="validAdd(index)" size="small" @click="addRowBelow(index)">新增</LinkBtn>
            <LinkBtn v-if="validRemove(index)" size="small" @click="removeRow(index)">删除</LinkBtn>
          </div>
        </template>
        <template #levelSlot="{ row, index }">
          <Select v-model="row.level" clearable filterable @on-change="handleChange(index, 'level', row)"
          >
            <Option v-for="item in levelOptions" :value="item.value" :key="item.value">{{ item.name }}</Option>
          </Select>
        </template>
        <template #threshValueSlot="{ row, index }">
          <div v-if="row.type != 7 && row.type != 21 && row.type != 22 && row.type != 23">
            <span v-if="row.type == 16 || row.type == 17">&lt;</span>
            <span v-else>&gt;</span>
            <InputNumber v-model="row.threshValue" :step="0.1" @on-change="handleChange(index, 'threshValue', row)" disabled>
            </InputNumber>
            <span v-if="row.type == 12 || row.type == 16 || row.type == 17 || row.type == 19">m</span>
            <span v-if="row.type == 9 || row.type == 20 || row.type == 13 || row.type == 14 || row.type == 15">°</span>
            <span v-if="row.type == 10">t*m</span>
            <span v-if="row.type == 11 || row.type == 18">t</span>
            <span v-if="row.type == 8">m/s</span>
            <span>告警</span>
          </div>
        </template>
        <template #enabledSlot="{ row, index }">
          <Switch v-model="row.enabled" :true-value="0" :false-value="1"
            @on-change="handleChange(index, 'enabled', row)" disabled>
            <span slot="open">开</span>
            <span slot="close">关</span>
          </Switch>
        </template>
        <template #pushMethSlot="{ row, index }">
          <!-- @on-change="changePushMeth(row, index)"  -->
          <Select v-model="row.pushMethArr" multiple filterable @on-change="handleChange(index, 'pushMeth', row)">
            <Option v-for="item in pushMethOptions" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </template>
        <template #pushUserNamesSlot="{ row, index }">
          <LinkBtn size="small" @click="openSmsReceiverModal(index, row)">{{ row.pushUserNames || '点击设置' }}</LinkBtn>
        </template>
        <template #handleUserNamesSlot="{ row, index }">
          <Select v-model="row.handleUserIdsArr" multiple filterable
            @on-change="handleChange(index, 'handleUserIds', row)">
            <Option v-for="item in handleUserNameOptions" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </template>
      </Table>
      <Button type="primary" @click="save">确定</Button>
      <Button @click="cancel">取消</Button>
    </Card>
    <!-- 短信接收人设置弹窗 -->
    <CustomModal v-model="showSmsReceiverModal" @on-ok="saveSmsReceivers" title="短信接收人设置" width="500">
      <div class="sms-receiver-form">
        <div class="form-header">
          <div class="header-item">姓名<span style="color: #ed4014">*</span></div>
          <div class="header-item">电话<span style="color: #ed4014">*</span></div>
          <div class="header-item">操作</div>
        </div>
        <div v-for="(item, index) in smsReceivers" :key="index" class="form-row">
          <AutoComplete v-model="item.name" placeholder="请输入姓名" style="width: 180px"
            :class="{ 'error-input': item.nameError }" @on-blur="validateReceiver(item)"
            @on-select="onNameSelect($event, item)" @on-change="onNameSelect($event, item)">
            <Option v-for="option in userOptions" :value="option.name" :key="option.name">
              <span class="demo-auto-complete-title">{{ option.name }}</span>
              <span class="demo-auto-complete-count">({{ option.phone }})</span>
            </Option>
          </AutoComplete>
          <Input v-model="item.phone" clearable placeholder="请输入电话" maxlength="11" style="width: 180px"
            :class="{ 'error-input': item.phoneError }" @on-blur="validateReceiver(item)" />
          <Button type="text" @click="removeSmsReceiver(index)" :disabled="smsReceivers.length === 1">
            <Icon type="md-close" size="18" color="#ed4014" />
          </Button>
        </div>
        <div class="form-footer">
          <Button type="primary" ghost size="small" icon="md-add" @click="addSmsReceiver">新增</Button>
        </div>
      </div>
    </CustomModal>
  </div>
</template>

<script>
import Util from '@/libs/util'
// import AlarmLogDetail from './alarm-log-detail';

const defaultSearch = {
  category: 5
};

export default {
  components: {
    // AlarmLogDetail
  },
  data() {
    return {
      userOptions: [{ name: '三个', phone: '18888888888' }, { name: '三个', phone: '18888888889' }, { name: '四个', phone: '18888888889' }],
      deduplicationTime: '', // 告警去重时间 单位min
      tbHeight: 400,
      searchObj: Util.objClone(defaultSearch),
      tableData: [],
      columns: [
        { type: 'index', title: '序号', width: 60, align: 'center' },
        { title: '告警类型', key: 'text', tooltip: true },
        { title: '告警等级', slot: 'levelSlot', tooltip: true },
        // { title: '阈值设置', slot: 'threshValueSlot', tooltip: true, width: 180 },
        { title: '状态', slot: 'enabledSlot', tooltip: true },
        { title: '通知方式', slot: 'pushMethSlot', tooltip: true },
        { title: '短信接收人', slot: 'pushUserNamesSlot', tooltip: true },
        { title: '告警处理人', slot: 'handleUserNamesSlot', align: 'center', tooltip: true },
        // { title: '操作', slot: 'actionSlot', width: 120, align: 'center' }
      ],
      levelOptions: [
        { value: '1', name: 'I级' },
        { value: '2', name: 'II级' },
        { value: '3', name: 'III级' }
      ],
      pushMethOptions: [
        { id: '1', name: '站内' },
        { id: '2', name: '短信' }
      ],
      handleUserNameOptions: [],
      // 短信接收人弹窗相关
      showSmsReceiverModal: false,
      currentRowIndex: -1,
      currentRow: null,
      smsReceivers: []
    };
  },
  mounted() {
    this.getData();
    this.getUserData();
  },
  methods: {
    getUserData() {
      Util.request('/rosterPersonnel/getPage', {
        page: {
          current: 1,
          size: -1
        },
        customQueryParams: {}
      }, 'post').then(resp => {
        this.handleUserNameOptions = resp.data.data.records.map((el) => {
          return {
            name: el.realName,
            id: el.id + ''
          }
        });
        console.log('this.handleUserNameOptions>>>>>', this.handleUserNameOptions);

      });
    },
    getData() {
      Util.request('/giveAlarmTypeConfig/selectList', this.searchObj, 'post').then(resp => {
        this.tableData = resp.data.data;
        this.tableData.forEach(item => {
          this.deduplicationTime = item.deduplicationTime
          if (item.pushMeth) {
            item.pushMethArr = item.pushMeth.split(',');
          } else {
            item.pushMethArr = [];
          }
          if (item.handleUserIds) {
            item.handleUserIdsArr = item.handleUserIds.split(',')
          } else {
            item.handleUserIdsArr = []
          }
          item.threshValue = item.threshValue ? item.threshValue : 0
          item.level = item.level ? item.level : ''
        });

        if (this.tableData.length < 7) {
          this.tbHeight = '';
        } else {
          this.tbHeight = 400;
        }
      });
    },
    handleDeduplicationTime(val) {
      this.tableData.forEach(item => {
        item.deduplicationTime = val
      });
    },
    // 在当前行下方添加新行
    addRowBelow(index) {
      const currentRow = this.tableData[index]
      const newRow = {
        ...currentRow,
        level: '',
        enabled: 1,
        id: ''
      }
      this.tableData.splice(index + 1, 0, newRow)
      this.$Message.info(`已在第 ${index + 1} 行下方添加新行`)
    },
    // 删除行
    removeRow(index) {
      if (!this.validRemove(index)) {
        this.$Message.info(`该告警类型最少存在一个`)
        return
      }
      this.$Modal.confirm({
        title: '确认删除',
        content: `删除后需保存后生效，确定要删除第 ${index + 1} 行吗？`,
        onOk: () => {
          this.tableData.splice(index, 1)
          this.$Message.success('删除成功')
        }
      })
    },
    validRemove(index) {
      const text = this.tableData[index].text
      let cnt = 0;
      this.tableData.forEach(element => {
        if (element.text == text) {
          cnt++;
        }
      });
      return cnt > 1
    },

    validAdd(index) {
      const text = this.tableData[index].text
      let cnt = 0;
      this.tableData.forEach(element => {
        if (element.text == text) {
          cnt++;
        }
      });
      return cnt < 3
    },

    // 单个字段变化时触发
    handleChange(index, key, row) {
      console.log(`第${index + 1}行 ${key} 修改为:`, row[key])
      switch (key) {
        case 'deduplicationTime':
          this.tableData = this.tableData.map((el) => {
            el.deduplicationTime = this.deduplicationTime
            return el
          })
          this.tableData[index].level = row.level
          break;
        case 'level':
          this.tableData[index].level = row.level
          break;
        case 'threshValue':
          this.tableData[index].threshValue = row.threshValue
          break;
        case 'enabled':
          this.tableData[index].enabled = row.enabled
          break;
        case 'pushMeth':
          this.tableData[index].pushMethArr = row.pushMethArr
          this.tableData[index].pushMeth = row.pushMethArr.join(',')
          break;
        case 'handleUserIds':
          this.tableData[index].handleUserIdsArr = row.handleUserIdsArr
          this.tableData[index].handleUserIds = row.handleUserIdsArr.join(',')
          this.tableData[index].handleUserNames = this.handleUserNameOptions
            .filter(el => row.handleUserIdsArr.includes(el.id))
            .map(el => el.name)
            .join(',')
          break;

        default:
          break;
      }
    },

    save() {
      // 获取最新的表格数据
      let data = this.tableData.map(item => {
        const { level, threshValue, pushMethArr, ...rest } = item;
        return {
          ...rest,
          level: level || '',
          threshValue: threshValue || '',
          pushMeth: (pushMethArr && pushMethArr.length > 0) ? pushMethArr.join(',') : ''
        };
      });

      // 检查每个告警类型下的告警等级是否有重复
      const alarmTypeGroups = {};
      for (const item of data) {
        if (!alarmTypeGroups[item.text]) {
          alarmTypeGroups[item.text] = new Set();
        }
        if (item.level) {
          if (alarmTypeGroups[item.text].has(item.level)) {
            this.$Message.error(`告警类型"${item.text}"下存在重复的告警等级`);
            return;
          }
          alarmTypeGroups[item.text].add(item.level);
        }
      }
      for (const item of data) {
        if (item.enabled == 0 && !item.level) {
          this.$Message.error(`请选择告警类型"${item.text}"下的告警等级`);
          return;
        }
      }
      console.log('保存>>>>>>>>>', this.tableData);
      // 提交数据到服务器
      Util.request('/giveAlarmTypeConfig/saveBatch', data, 'post').then(resp => {
        this.$Message.success('保存成功');
        this.getData();
      }).catch(err => {
        this.$Message.error('保存失败');
      });
    },
    cancel() {
      // 重新加载数据
      this.getData();
    },
    changePushMeth(row) {
      row.pushMeth = row.pushMethArr.join(',');
      console.log('changePushMeth>>>>>>>>>>>', row);

    },

    // 短信接收人相关方法
    openSmsReceiverModal(index, row) {
      this.currentRowIndex = index;
      this.currentRow = row;
      this.smsReceivers = [];

      // 如果已有接收人数据，解析并填充
      if (row.pushUserNames && row.pushUserPhones) {
        const names = row.pushUserNames.split(',');
        const phones = row.pushUserPhones.split(',');

        // 确保名称和电话数量一致
        const minLength = Math.min(names.length, phones.length);
        for (let i = 0; i < minLength; i++) {
          this.smsReceivers.push({
            name: names[i],
            phone: phones[i]
          });
        }
      }

      // 如果没有数据，至少添加一个空行
      if (this.smsReceivers.length === 0) {
        this.addSmsReceiver();
      }

      this.userOptions = []
      this.tableData.forEach(item => {
        // 如果已有接收人数据，解析并填充
        if (item.pushUserNames && item.pushUserPhones) {
          const names = item.pushUserNames.split(',');
          const phones = item.pushUserPhones.split(',');

          // 确保名称和电话数量一致
          const minLength = Math.min(names.length, phones.length);
          for (let i = 0; i < minLength; i++) {
            this.userOptions.push({
              name: names[i],
              phone: phones[i]
            });
          }
        }
      });
      this.userOptions = this.userOptions.filter((item, index, self) =>
        index === self.findIndex((t) => (
          t.name === item.name && t.phone === item.phone
        ))
      );

      this.showSmsReceiverModal = true;
    },

    addSmsReceiver() {
      this.smsReceivers.push({
        name: '',
        phone: ''
      });
    },

    removeSmsReceiver(index) {
      this.smsReceivers.splice(index, 1);
      // 如果删除后没有接收人，添加一个空行
      if (this.smsReceivers.length === 0) {
        this.addSmsReceiver();
      }
    },

    validateReceiver(item) {
      // 重置错误状态
      item.nameError = false;
      item.phoneError = false;

      // 验证姓名
      if (!item.name || item.name.trim() === '') {
        item.nameError = true;
      }

      // 验证手机号
      if (!item.phone) {
        item.phoneError = true;
      } else if (!/^1\d{10}$/.test(item.phone)) {
        item.phoneError = true;
      }
    },

    saveSmsReceivers() {
      // 验证所有接收人数据
      let isValid = true;
      let names = [];
      let phones = [];
      console.log('smsReceivers>>>>>>>>>>>>>', this.smsReceivers);
      for (let i = 0; i < this.smsReceivers.length; i++) {
        const item = this.smsReceivers[i];
        this.validateReceiver(item);

        if (item.nameError || item.phoneError) {
          isValid = false;
          if (item.nameError) {
            this.$Message.error('请输入姓名');
          } else if (item.phoneError) {
            this.$Message.error('请输入正确的手机号码');
          }
          break;
        }

        names.push(item.name);
        phones.push(item.phone);
      }

      if (!isValid) return;
      console.log('isValid>>>>>>>>>>>>>', names, phones);
      // 保存数据
      if (this.currentRow) {
        this.currentRow.pushUserNames = names.join(',');
        this.currentRow.pushUserPhones = phones.join(',');
      }
      if (this.currentRowIndex != -1) {
        this.tableData[this.currentRowIndex].pushUserNames = names.join(',');
        this.tableData[this.currentRowIndex].pushUserPhones = phones.join(',');
      }
      console.log('>>>>>>>>>>>>>', this.tableData[this.currentRowIndex]);

      this.showSmsReceiverModal = false;
      this.$Message.success('设置成功');
    },

    cancelSmsReceiverModal() {
      this.showSmsReceiverModal = false;
    },

    onNameSelect(value, item) {
      const obj = this.userOptions.map(el => el).find(el => el.name == value)
      if (obj != null) {
        item.phone = obj.phone
      }

      console.log('选中>>>', this.smsReceivers);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ivu-table-wrapper{
  overflow: visible;
}
.top-wrapper {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

.sms-receiver-form {
  padding: 0 20px;

  .form-header {
    display: flex;
    margin-bottom: 10px;

    .header-item {
      flex: 1;
      text-align: center;
      font-weight: bold;

      &:last-child {
        flex: 0 0 60px;
      }
    }
  }

  .form-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    >* {
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
        flex: 0 0 60px;
        text-align: center;
      }
    }
  }

  .form-footer {
    text-align: center;
    margin-top: 20px;
  }

  .error-input {
    border-color: #ed4014;

    &:hover,
    &:focus {
      border-color: #ed4014;
    }
  }
}
</style>
