<template>
	<div class="ai-security-page">
		<div class="ai-security-container">
			<!-- 左侧折线图 -->
			<Card class="chart-card">
				<div class="card-header">
					<img
						src="../../../../images/icon/titleIcon.png"
						style="width: 20px"
					/>
					<span class="card-title">今日告警趋势</span>
				</div>
				<div class="today-stats">
					<div class="stats-label">今日告警总数</div>
					<div class="stats-value">{{ todayWarnInfo.sum }}</div>
				</div>
				<div class="chart-container">
					<div
						ref="lineChart"
						class="chart-wrapper"
						style="width: 100%; height: 100%"
					></div>
				</div>
			</Card>

			<!-- 中间AI行为告警数量 -->
			<Card class="ratio-card middle">
				<div class="ai-alarm-container">
					<div class="section-title">
						<img
							src="../../../../images/icon/bing.png"
							style="width: 20px"
						/>
						<span>AI行为告警数量</span>
					</div>
					<div class="alarm-list-container">
						<div
							class="alarm-list"
							v-for="(item, index) in alarmCounts"
							:key="index"
						>
							<div class="alarm-item">
								<div class="alarm-info">
									<span class="alarm-label">{{
										item.name
									}}</span>
									<div class="alarm-progress">
										<div
											class="progress-bar"
											:style="{
												width:
													getPercentage(item.sum) +
													'%',
											}"
										></div>
									</div>
									<span class="alarm-value"
										>{{ item.sum }} 个</span
									>
								</div>
							</div>
						</div>
					</div>
				</div>
			</Card>

			<!-- 右侧饼状图 -->
			<Card class="pie-card">
				<div class="pie-container">
					<div class="pie-header">
						<img
							src="../../../../images/icon/bing.png"
							style="width: 20px"
						/>
						<span class="pie-title">告警类型占比</span>
					</div>
					<div class="pie-chart-wrapper">
						<div ref="pieChart" class="pie-chart"></div>
					</div>
					<div
						class="legend"
						ref="legendContainer"
						@mouseenter="pauseScroll"
						@mouseleave="resumeScroll"
					>
						<div class="legend-content" ref="legendContent">
							<div
								class="legend-item"
								v-for="(item, index) in alarmTypes"
								:key="index"
							>
								<div
									class="legend-dot"
									:style="{ backgroundColor: item.color }"
								></div>
								<span class="legend-label">{{
									item.name
								}}</span>
								<span class="legend-value"
									>{{ getPiePercentage(item.sum) }}%</span
								>
							</div>
						</div>
					</div>
				</div>
			</Card>
		</div>
		<ContentCard class="container">
			<ATabs
				v-model="activeTab"
				@change="handleTabChange"
				:tabList="tabList"
			/>

			<!-- 筛选条件 -->
			<div class="filter-section">
				<div class="filter-item">
					<label>识别行为</label>
					<Select
						v-model="filterForm.behavior"
						@on-change="handleBehaviorChange"
						placeholder="请选择"
						style="width: 200px"
					>
						<Option value="全部" label="全部">全部</Option>
						<Option
							v-for="(item, index) in warnNumList"
							:key="index"
							:value="item.name"
							:label="item.name"
							>{{ item.name }}</Option
						>
					</Select>
				</div>

				<div class="filter-item">
					<label>抓拍日期</label>
					<DatePicker
						v-model="filterForm.date"
						type="daterange"
						placeholder="请选择"
						style="width: 200px"
					/>
				</div>

				<div class="filter-buttons">
					<Button
						type="primary"
						icon="ios-search"
						@click="handleSearch"
						>查询</Button
					>
					<Button icon="ios-refresh" @click="handleReset"
						>重置</Button
					>
					<!-- <Button icon="ios-download-outline" @click="handleExport">导出</Button> -->
				</div>
			</div>

			<!-- 图片网格 -->
			<div class="image-grid">
				<div
					v-for="(item, index) in captureList"
					:key="index"
					class="image-card"
					:class="{ selected: item.selected }"
					@click="toggleSelection(index)"
				>
					<!-- <div class="card-checkbox">
              <Checkbox v-model="item.selected" @on-change="toggleSelection(index)"></Checkbox>
            </div> -->

					<div
						class="image-container"
						@click.stop="previewImage(item)"
					>
						<img
							v-if="getImageUrl(item)"
							:src="getImageUrl(item)"
							alt="抓拍图片"
						/>
						<div v-else class="no-video">
							<Icon
								type="md-videocam-off"
								size="40"
								color="#666"
							/>
							<span>暂无视频信号</span>
						</div>
						<div v-if="getImageUrl(item)" class="preview-overlay">
							<Icon type="md-eye" size="24" color="#fff" />
						</div>
					</div>

					<div class="card-info">
						<div class="alarm-tag">
							<Icon type="md-warning" />
							<span>{{ item.aiRuleTypeI18n }}</span>
						</div>
						<div class="capture-time">
							<Icon type="md-time" />
							<span>{{ item.createTime }}</span>
						</div>
					</div>
				</div>
			</div>

			<!-- 分页器 -->
			<div class="pagination-container">
				<!-- <div class="pagination-info">
            <span>共{{ totalCount }}条</span>
          </div> -->
				<Page
					:current="currentPage"
					:total="totalCount"
					:page-size="pageSize"
					show-elevator
					show-sizer
					show-total
					@on-change="handlePageChange"
					@on-page-size-change="handlePageSizeChange"
					:page-size-opts="[8, 16, 32, 64, 128]"
				/>
			</div>
		</ContentCard>

		<!-- 图片预览弹窗 -->
		<Modal
			v-model="previewModal"
			:closable="false"
			:mask-closable="true"
			:width="800"
			class="image-preview-modal"
			@on-cancel="closePreview"
		>
			<div class="preview-container">
				<div class="preview-header">
					<h3>图片预览</h3>
				</div>

				<div class="preview-content">
					<div class="preview-image">
						<img
							:src="currentPreviewImage"
							alt="预览图片"
							@error="handleImageError"
							style="
								width: 100%;
								height: 100%;
								object-fit: contain;
							"
						/>
					</div>

					<div class="preview-info" v-if="currentPreviewItem">
						<div class="info-item">
							<span class="label">告警类型：</span>
							<span class="value">{{
								currentPreviewItem.aiRuleTypeI18n
							}}</span>
						</div>
						<div class="info-item">
							<span class="label">抓拍时间：</span>
							<span class="value">{{
								currentPreviewItem.createTime
							}}</span>
						</div>
						<div
							class="info-item"
							v-if="currentPreviewItem.deviceName"
						>
							<span class="label">设备名称：</span>
							<span class="value">{{
								currentPreviewItem.deviceName
							}}</span>
						</div>
						<div
							class="info-item"
							v-if="currentPreviewItem.location"
						>
							<span class="label">位置信息：</span>
							<span class="value">{{
								currentPreviewItem.location
							}}</span>
						</div>
					</div>
				</div>
			</div>
			<div slot="footer" class="preview-footer">
				<Button type="primary" @click="closePreview">关闭</Button>
			</div>
		</Modal>
	</div>
</template>

<script>
import * as echarts from "echarts";
import Util from "@/libs/util";

export default {
	data() {
		return {
			lineChart: null,
			pieChart: null,
			// 今日告警总数
			currentAlarm: 1000,
			// AI行为告警数量
			alarmCounts: [],
			// 告警类型占比数据
			alarmTypes: [],
			// 当日抓拍总数
			currentCapture: 87634,
			activeTab: "0",
			tabList: [
				{
					label: "全部",
				},
				{
					label: "烟火识别",
					value: "07,06",
				},
				{
					label: "外来人员识别",
					value: "12",
				},
				{
					label: "区域入侵",
					value: "03",
				},
				{
					label: "举手求救",
					value: "09",
				},
				{
					label: "跌倒",
					value: "04",
				},
				{
					label: "违规行为",
					value: "00,05,02,08,10,11",
				},
			],
			// 筛选表单
			filterForm: {
				behavior: "",
				date: "",
			},
			// 抓拍列表数据
			captureList: [],
			// 分页数据
			currentPage: 1,
			pageSize: 8,
			totalCount: 0,
			todayWarnInfo: {
				abscissa: [],
				sum: 0,
				sums: [],
			},
			color: [
				"#4A90E2",
				"#7ED321",
				"#F5A623",
				"#D63384",
				"#FF6B6B",
				"#4ECDC4",
			],
			warnNumList: [],
			selectedItem: "",
			navItem: "",
			// 图片预览相关
			previewModal: false,
			currentPreviewImage: "",
			currentPreviewItem: null,
			// 自动滚动相关
			scrollInterval: null,
			isScrolling: false,
			scrollDirection: 1,
			scrollSpeed: 1,
		};
	},
	mounted() {
		this.initRequest();
		this.handleSearch();
		this.initCaptureList();

		// 添加键盘事件监听
		document.addEventListener("keydown", this.handleKeyDown);

		// 初始化自动滚动
		this.$nextTick(() => {
			this.initAutoScroll();
		});
	},

	beforeDestroy() {
		// 移除键盘事件监听
		document.removeEventListener("keydown", this.handleKeyDown);

		// 清理滚动定时器
		if (this.scrollInterval) {
			clearInterval(this.scrollInterval);
		}

		if (this.lineChart) {
			this.lineChart.dispose();
		}
		if (this.pieChart) {
			this.pieChart.dispose();
		}
	},
	methods: {
		// 初始化自动滚动
		initAutoScroll() {
			this.$nextTick(() => {
				const container = this.$refs.legendContainer;
				const content = this.$refs.legendContent;

				if (!container || !content) return;

				// 检查是否需要滚动（内容高度是否超过容器高度）
				if (content.scrollHeight <= container.clientHeight) {
					return; // 内容不需要滚动
				}

				// 开始自动滚动
				this.startAutoScroll();
			});
		},

		// 开始自动滚动
		startAutoScroll() {
			if (this.scrollInterval) {
				clearInterval(this.scrollInterval);
			}

			this.isScrolling = true;
			this.scrollInterval = setInterval(() => {
				const container = this.$refs.legendContainer;
				const content = this.$refs.legendContent;

				if (!container || !content) return;

				const containerHeight = container.clientHeight;
				const contentHeight = content.scrollHeight;
				const currentScrollTop = container.scrollTop;

				// 如果滚动到底部，改变方向向上滚动
				if (currentScrollTop >= contentHeight - containerHeight) {
					this.scrollDirection = -1;
				}
				// 如果滚动到顶部，改变方向向下滚动
				else if (currentScrollTop <= 0) {
					this.scrollDirection = 1;
				}

				// 执行滚动
				container.scrollTop += this.scrollDirection * this.scrollSpeed;
			}, 50); // 每50ms滚动一次
		},

		// 暂停滚动
		pauseScroll() {
			if (this.scrollInterval) {
				clearInterval(this.scrollInterval);
				this.scrollInterval = null;
			}
			this.isScrolling = false;
		},

		// 恢复滚动
		resumeScroll() {
			if (!this.isScrolling) {
				this.startAutoScroll();
			}
		},

		initRequest() {
			// 今日趋势
			Util.requestGet("/aiHostRecord/nowDaySnapTotleLineChart").then(
				(res) => {
					if (res.data.code == "success") {
						this.todayWarnInfo = res.data.data;
						setTimeout(() => {
							this.initLineChart();
						}, 500);
					}
				}
			);
			// AI告警数量
			Util.requestGet("/aiHostRecord/aiBehaviorAlerts").then((res) => {
				if (res.data.code == "success") {
					this.alarmCounts = res.data.data.LeftList;
					this.warnNumList = res.data.data.LeftList;
				}
			});
			// 告警类别占比
			Util.requestGet("/aiHostRecord/getViolationBehaviorPieChart").then(
				(res) => {
					if (res.data.code == "success") {
						this.alarmTypes = res.data.data.LeftList.map(
							(i, index) => {
								return {
									name: i.name,
									sum: i.sum,
									color: this.color[index],
								};
							}
						);
						setTimeout(() => {
							this.initPieChart();
							// 重新初始化自动滚动
							this.initAutoScroll();
						}, 500);
					}
				}
			);
			Util.requestGet("/sys/dict/getDicItem/ai_rule_type").then((res) => {
				if (res.data.code == "success") {
					console.log(res);
				}
			});
		},

		handleBehaviorChange(e) {
			let value = "";

			// 如果选择"全部"或为空，设置为空字符串
			if (e === "全部") {
				value = this.tabList.find((i) => i.label == "违规行为").value;
			} else {
				switch (e) {
					case "未戴安全帽":
						value = "00";
						break;
					case "未穿反光服":
						value = "05";
						break;
					case "吸烟":
						value = "02";
						break;
					case "打架":
						value = "08";
						break;
					case "未系安全带":
						value = "10,11";
						break;
				}
			}

			// 5 违规行为
			let index = this.tabList.findIndex((i) => i.label == "违规行为");
			if (this.tabList[index].value == this.navItem) {
				this.navItem = "";
			}

			this.selectedItem = value;
		},

		initDate(time) {
			// 获取当前时间
			if (!time) {
				return "";
			}
			// 2025-07-18
			let now = new Date(time);
			let year = now.getFullYear();
			let month = now.getMonth() + 1; // 两位数
			let day = now.getDate(); // 两位数
			return (
				year +
				"-" +
				String(month).padStart(2, "0") +
				"-" +
				String(day).padStart(2, "0")
			);
		},

		handleSearch() {
			let aiRuleTypes = "";

			// 如果选择了特定行为，则组合selectedItem和navItem
			if (this.selectedItem) {
				if (this.navItem) {
					aiRuleTypes = this.selectedItem + "," + this.navItem;
				} else {
					aiRuleTypes = this.selectedItem;
				}
			} else {
				// 如果选择"全部"，则只使用navItem
				aiRuleTypes = this.navItem || "";
			}

			let data = {
				page: {
					current: this.currentPage,
					size: this.pageSize,
				},
				customQueryParams: {
					aiRuleTypes: aiRuleTypes,
					startCreate: this.initDate(this.filterForm.date[0]),
					endCreate: this.initDate(this.filterForm.date[1]),
				},
			};
			Util.requestPost("/aiHostRecord/selectPage", data).then((res) => {
				if (res.data.code == "success") {
					this.captureList = res.data.data.records.map((i) => {
						return {
							...i,
							selected: false,
						};
					});
					this.totalCount = res.data.data.total;
				}
			});
		},

		// 计算百分比
		getPercentage(value) {
			if (!this.alarmCounts.length) return 0;

			// 计算总数
			const total = this.alarmCounts.reduce(
				(sum, item) => sum + item.sum,
				0
			);

			// 如果总数为0，返回0
			if (total === 0) return 0;

			// 计算百分比，保留一位小数
			const percentage = (value / total) * 100;
			return Math.round(percentage);
		},

		// 计算饼图百分比
		getPiePercentage(value) {
			if (!this.alarmTypes.length) return 0;

			// 计算总数
			const total = this.alarmTypes.reduce(
				(sum, item) => sum + item.sum,
				0
			);

			// 如果总数为0，返回0
			if (total === 0) return 0;

			// 计算百分比，保留一位小数
			const percentage = (value / total) * 100;
			return Math.round(percentage);
		},

		handleTabChange(e) {
			this.activeTab = e;
			this.navItem = this.tabList[e].value;
			this.filterForm.behavior = "";
			this.filterForm.date = "";
			this.selectedItem = "";
			this.currentPage = 1;
			this.handleSearch();
		},

		initLineChart() {
			let dom = document.querySelector(".chart-container");
			document.querySelector(".chart-wrapper").style.height =
				dom.offsetHeight + "px";
			document.querySelector(".chart-wrapper").style.width =
				dom.offsetWidth + "px";
			this.lineChart = echarts.init(this.$refs.lineChart);

			const option = {
				tooltip: {
					trigger: "axis",
				},
				grid: {
					left: "10%",
					right: "10%",
					bottom: "15%",
					top: "10%",
				},
				xAxis: {
					type: "category",
					data: this.todayWarnInfo.abscissa,
					axisLine: {
						lineStyle: {
							color: "#e8e8e8",
						},
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						color: "#666",
					},
				},
				yAxis: {
					type: "value",
					max: 100,
					splitLine: {
						lineStyle: {
							color: "#f0f0f0",
						},
					},
					axisLine: {
						show: false,
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						color: "#666",
					},
				},
				series: [
					{
						data: this.todayWarnInfo.sums,
						type: "line",
						lineStyle: {
							color: "#1890ff",
							width: 2,
						},
						symbol: "circle",
						symbolSize: 6,
						itemStyle: {
							color: "#1890ff",
						},
						areaStyle: {
							color: new echarts.graphic.LinearGradient(
								0,
								0,
								0,
								1,
								[
									{
										offset: 0,
										color: "rgba(24, 144, 255, 0.3)",
									},
									{
										offset: 1,
										color: "rgba(24, 144, 255, 0.1)",
									},
								]
							),
						},
					},
				],
			};

			this.lineChart.setOption(option);
		},

		initPieChart() {
			let data = this.alarmTypes.map((i) => {
				return {
					value: this.getPiePercentage(i.sum),
					name: i.name,
					itemStyle: {
						color: i.color,
					},
				};
			});

			let dom = document.querySelector(".pie-chart-wrapper");
			document.querySelector(".pie-chart").style.height =
				dom.clientHeight + "px";
			document.querySelector(".pie-chart").style.width =
				dom.clientWidth + "px";
			this.pieChart = echarts.init(this.$refs.pieChart);

			const option = {
				tooltip: {
					trigger: "item",
					formatter: "{a} <br/>{b}: {c}%",
				},
				series: [
					{
						name: "告警类型占比",
						type: "pie",
						radius: "100%",
						center: ["50%", "50%"],
						data: data,
						avoidLabelOverlap: false,
						itemStyle: {
							borderRadius: 5,
							borderColor: "#fff",
							borderWidth: 2,
						},
						emphasis: {
							itemStyle: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: "rgba(0, 0, 0, 0.5)",
							},
						},
						label: {
							show: false,
						},
						labelLine: {
							show: false,
						},
					},
				],
			};

			this.pieChart.setOption(option);
		},

		// 初始化抓拍列表数据
		initCaptureList() {
			this.captureList = [];
			const alarmTypes = ["未戴安全帽", "未穿反光衣", "吸烟", "打架"];

			for (let i = 0; i < this.pageSize; i++) {
				this.captureList.push({
					id: i + 1,
					image: null, // 暂无图片，显示占位符
					alarmType: alarmTypes[i % alarmTypes.length],
					time: "2024-11-07 15:04:00",
					selected: i === 0 || i === 5, // 默认选中第一个和第六个，模拟图片效果
				});
			}
		},

		// 切换选中状态
		toggleSelection(index) {
			this.captureList[index].selected =
				!this.captureList[index].selected;
		},

		// 获取图片URL
		getImageUrl(item) {
			// 常见的图片字段名
			return (
				item.snapUrl ||
				item.imageUrl ||
				item.picUrl ||
				item.image ||
				item.imgUrl ||
				item.photoUrl ||
				""
			);
		},

		// 预览图片
		previewImage(item) {
			const imageUrl = this.getImageUrl(item);
			if (imageUrl) {
				this.currentPreviewImage = imageUrl;
				this.currentPreviewItem = item;
				this.previewModal = true;
			}
		},

		// 关闭预览
		closePreview() {
			this.previewModal = false;
			this.currentPreviewImage = "";
			this.currentPreviewItem = null;
		},

		// 图片加载错误处理
		handleImageError(event) {
			console.warn("图片加载失败:", event.target.src);
			// 可以设置默认图片或显示错误信息
		},

		// 键盘事件处理
		handleKeyDown(event) {
			if (event.key === "Escape" && this.previewModal) {
				this.closePreview();
			}
		},

		// // 处理搜索
		// handleSearch() {
		//   console.log('搜索条件:', this.filterForm)
		//   // 这里可以调用API进行搜索
		//   this.currentPage = 1
		//   this.initCaptureList()
		// },

		// 重置筛选条件
		handleReset() {
			this.filterForm = {
				behavior: "",
				date: "",
			};
			this.selectedItem = "";
			this.currentPage = 1;
			this.handleSearch();
		},

		// 导出数据
		handleExport() {
			console.log("导出数据");
			// 这里可以调用导出API
		},

		// 分页切换
		handlePageChange(page) {
			this.currentPage = page;
			this.handleSearch();
		},

		// 分页大小切换
		handlePageSizeChange(size) {
			this.pageSize = size;
			this.currentPage = 1;
			this.handleSearch();
		},
	},
};
</script>

<style lang="less" scoped>
.preview-footer {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
/deep/ .ivu-tabs-bar.ivu-tabs-tab {
	background: #f2f3f5 !important;
}
.middle {
	/deep/ .ivu-card-body {
		height: calc(~"100%") !important;
	}
}
/deep/ .ivu-card-body {
	height: calc(~"100%");
	background-color: #fafcff;
}
.container {
	width: 100%;
	height: auto;
	min-height: 40vh;
	margin-top: 20px;
}
.ai-security-page {
	padding: 20px;
	background: #f0f2f5;
}

.page-header {
	display: flex;
	align-items: center;
	margin-bottom: 20px;

	.page-title {
		margin: 0 0 0 10px;
		font-size: 18px;
		font-weight: 600;
		color: #262626;
	}
}

.ai-security-container {
	display: flex;
	gap: 20px;
	height: 40vh;
}

.chart-card {
	width: 40%;
	height: 100%;
	.card-header {
		display: flex;
		align-items: center;
		margin-bottom: 15px;

		.card-title {
			margin-left: 8px;
			font-size: 16px;
			font-weight: 500;
			color: #262626;
		}
	}

	.today-stats {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;

		.stats-label {
			font-size: 14px;
			color: #666;
			margin-bottom: 5px;
		}

		.stats-value {
			font-size: 24px;
			font-weight: 600;
			color: #1890ff;
			margin-bottom: 2px;
		}

		.stats-unit {
			font-size: 12px;
			color: #999;
		}
	}

	.chart-container {
		width: 100%;
		height: calc(~"100% - 100px");

		.chart-wrapper {
			width: 100%;
			height: 100%;
		}
	}
}

.ratio-card {
	width: 30%;
	height: 100%;

	.ai-alarm-container {
		height: 100%;

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 20px;
			color: #333;
			font-size: 16px;
			font-weight: 500;

			span {
				margin-left: 8px;
			}
		}

		.alarm-list-container {
			height: calc(~"100% - 80px");
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			gap: 10px;
			overflow-y: scroll;

			.alarm-list {
				.alarm-item {
					.alarm-info {
						display: flex;
						align-items: center;

						.alarm-label {
							width: 80px;
							font-size: 14px;
							color: #666;
							margin-right: 10px;
						}

						.alarm-progress {
							flex: 1;
							height: 8px;
							background: #f0f0f0;
							border-radius: 4px;
							overflow: hidden;
							margin-right: 10px;

							.progress-bar {
								height: 100%;
								background: linear-gradient(
									90deg,
									#a8c4fa 0%,
									#81a4f8 100%
								);
								border-radius: 4px;
								transition: width 0.3s ease;
							}
						}

						.alarm-value {
							font-size: 14px;
							font-weight: 600;
							color: #1890ff;
							min-width: 50px;
							text-align: right;
						}
					}
				}
			}
		}
	}
}

.pie-card {
	width: 30%;
	height: 100%;

	.pie-container {
		display: flex;
		flex-direction: column;
		height: calc(~"100%");

		.pie-header {
			display: flex;
			margin-bottom: 15px;

			.pie-title {
				margin-left: 8px;
				font-size: 16px;
				font-weight: 500;
				color: #262626;
			}
		}

		.pie-chart-wrapper {
			width: 100%;
			height: 250px;
			margin-bottom: 15px;

			.pie-chart {
				width: 100%;
				height: 100%;
			}
		}

		.legend {
			width: 100%;
			height: 70px;
			overflow-y: auto;
			position: relative;
			scroll-behavior: smooth;

			&::-webkit-scrollbar {
				display: none;
			}

			.legend-content {
				transition: transform 0.5s ease;
			}

			.legend-item {
				display: flex;
				align-items: center;
				margin-bottom: 6px;

				.legend-dot {
					width: 8px;
					height: 8px;
					border-radius: 50%;
					margin-right: 8px;
				}

				.legend-label {
					flex: 1;
					font-size: 12px;
					color: #666;
				}

				.legend-value {
					font-size: 12px;
					font-weight: 500;
					color: #262626;
				}
			}
		}
	}
}

/deep/ .ivu-card {
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/deep/ .ivu-card-body {
	padding: 20px;
}

/deep/ .ivu-progress-outer {
	background-color: #f0f0f0;
}

/* 筛选条件样式 */
.filter-section {
	display: flex;
	align-items: center;
	padding: 20px 0;
	background: #fff;
	border-bottom: 1px solid #e8e8e8;
	margin-bottom: 20px;

	.filter-item {
		display: flex;
		align-items: center;
		margin-right: 30px;

		label {
			font-size: 14px;
			color: #666;
			margin-right: 10px;
			white-space: nowrap;
		}
	}

	.filter-buttons {
		margin-left: auto;
		display: flex;
		gap: 10px;
	}
}

/* 图片网格样式 */
.image-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20px;
	margin-bottom: 30px;

	.image-card {
		position: relative;
		background: #fff;
		border-radius: 8px;
		overflow: hidden;
		cursor: pointer;
		transition: all 0.3s ease;
		border: 2px solid transparent;

		&.selected {
			border-color: #4a90e2;
			box-shadow: 0 0 0 1px #4a90e2;
		}

		&:hover {
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		}

		.card-checkbox {
			position: absolute;
			top: 8px;
			left: 8px;
			z-index: 10;

			/deep/ .ivu-checkbox-wrapper {
				background: rgba(255, 255, 255, 0.9);
				border-radius: 4px;
				padding: 2px;
			}
		}

		.image-container {
			width: 100%;
			height: 180px;
			background: #1a1a1a;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			color: #999;

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.no-video {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 100%;
				color: #999;

				span {
					margin-top: 10px;
					font-size: 14px;
				}
			}
		}

		.card-info {
			padding: 12px;

			.alarm-tag {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
				color: #ff4d4f;
				font-size: 14px;

				i {
					margin-right: 4px;
				}
			}

			.capture-time {
				display: flex;
				align-items: center;
				color: #999;
				font-size: 12px;

				i {
					margin-right: 4px;
				}
			}
		}
	}
}

/* 分页器样式 */
.pagination-container {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding: 20px 0;
	background: #fff;
	border-top: 1px solid #e8e8e8;

	.pagination-info {
		font-size: 14px;
		color: #666;
	}
}

/* 图片预览样式 */
.image-container {
	position: relative;
	cursor: pointer;
	overflow: hidden;
	border-radius: 8px;

	&:hover {
		.preview-overlay {
			opacity: 1;
		}
	}

	.preview-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0;
		transition: opacity 0.3s ease;
		border-radius: 8px;
	}
}

/deep/ .image-preview-modal {
	.ivu-modal-body {
		padding: 0;
		max-height: 80vh;
		overflow-y: auto;
	}

	.preview-container {
		.preview-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20px;
			border-bottom: 1px solid #e8e8e8;

			h3 {
				margin: 0;
				font-size: 18px;
				font-weight: 500;
				color: #262626;
			}

			.close-btn {
				padding: 4px;

				&:hover {
					background: #f5f5f5;
					border-radius: 4px;
				}
			}
		}

		.preview-content {
			padding: 20px;
			display: flex;
			flex-direction: column;
			gap: 20px;

			.preview-image {
				text-align: center;

				img {
					max-width: 100%;
					max-height: 500px;
					object-fit: contain;
					border-radius: 8px;
					box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
				}
			}

			.preview-info {
				background: #f8f9fa;
				padding: 16px;
				border-radius: 8px;

				.info-item {
					display: flex;
					align-items: center;
					margin-bottom: 12px;

					&:last-child {
						margin-bottom: 0;
					}

					.label {
						font-weight: 500;
						color: #666;
						min-width: 80px;
						margin-right: 8px;
					}

					.value {
						color: #262626;
						flex: 1;
					}
				}
			}
		}
	}
}
</style>
