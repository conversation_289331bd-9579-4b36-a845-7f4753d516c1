<template>
	<ContentCard class="container">
		<Row :gutter="16" style="height: 100%">
			<Col flex="200px">
				<div class="tree-container">
					<Tree
						:data="data"
						show-checkbox
						@on-check-change="handleCheckChange"
					></Tree>
				</div>
			</Col>
			<Col flex="408px" style="overflow: hidden">
				<div style="height: 100%; overflow-y: auto">
					<div class="searchInfo">
						<div class="searchInput">
							<span>设备名称/编号</span>
							<Input
								placeholder="请输入"
								style="flex: 1"
								v-model="searhhValue"
							/>
						</div>
						<div
							style="
								display: flex;
								gap: 5px;
								justify-content: flex-end;
							"
						>
							<Button
								type="primary"
								icon="ios-search"
								@click="handleSearch"
								>查询</Button
							>
							<Button
								type="default"
								icon="ios-refresh"
								@click="handleReset"
								>重置</Button
							>
						</div>
						<Table :columns="columns" :data="tableData" stripe>
							<template #location="{ row }">
								<RadioGroup
									v-model="row.locationType"
									@on-change="handleRadioChange($event, row)"
									vertical
								>
									<Radio :label="1">
										<span>北斗定位</span>
									</Radio>
									<Radio :label="2">
										<span>人工定位</span>
									</Radio>
									<Radio :label="3">
										<span>地图打点</span>
									</Radio>
								</RadioGroup>
							</template>
							<template #position="{ row }">
								<span v-if="row.equipmentLat"
									>北纬{{ row.equipmentLat }}</span
								><br />
								<span v-if="row.equipmentLng"
									>东经{{ row.equipmentLng }}</span
								><br />
								<Button
									type="primary"
									size="small"
									style="margin: 5px"
									@click="handleClick(row)"
									v-if="row.locationType === 3"
									>打点</Button
								>
							</template>
							<!-- <template #action="{ row }">
                <Button type="text" size="small" @click="handleDetele(row)"">删除</Button>
              </template> -->
						</Table>
						<Page
							:total="total"
							size="small"
							show-total
							:current="page.current"
							:page-size="page.size"
							:page-size-opts="[7, 14, 20, 30, 50, 100]"
							style="text-align: right"
							@on-change="handleCurrentChange"
							@on-page-size-change="handleSizeChange"
						/>
						<div class="button">
							<Button type="primary" @click="handleSave"
								>提交</Button
							>
							<Button type="default" @click="handleCancel"
								>取消</Button
							>
						</div>
					</div>
				</div>
			</Col>
			<Col flex="1" style="position: relative">
				<map3dbase :map-instance-id="'qjMap'" />
			</Col>
		</Row>
	</ContentCard>
</template>

<script>
import map3dbase from "@/components/CesiumBaseMap/index.vue";
import Util from "@/libs/util";
export default {
	name: "Panorama",
	components: {
		map3dbase,
	},
	data() {
		return {
			data: [
				{
					title: "全部",
					expand: true,
					children: [],
				},
			],
			columns: [
				{
					title: "设备名称/监测项名称",
					key: "equipmentName",
				},
				{
					title: "定位方式",
					slot: "location",
					width: 90,
				},
				{
					title: "经纬度",
					slot: "position",
				},
			],
			tableData: [],
			posi: {},
			searhhValue: "",
			names: [],
			requesetData: [],
			page: {
				current: 1,
				size: 10,
			},
			total: 0,
			activesItem: [],
		};
	},
	mounted() {
		this.$bus.on("setPointInfo", (posi, item) => {
			this.posi = posi;
			this.beforeSpliceRow(posi, item);
		});
		this.initTree();
	},
	methods: {
		handleSave() {
			if (this.requesetData.length === 0) {
				this.$Message.error("需要更改设备");
				return;
			}
			let url = `/mechanical/equipmentLocation/saveBatch`;
			Util.requestPost(url, this.requesetData).then((res) => {
				this.$Message.success("提交成功");
				this.requesetData = [];
				this.$bus.emit("disablePointing");
			});
		},
		handleCancel() {
			this.handleCheckChange(this.activesItem);
			this.$bus.emit("disablePointing");
		},
		beforeSpliceRow(posi, item) {
			this.tableData = this.tableData.map((i) => {
				if (i.equipmentId === item.equipmentId) {
					let is = {
						...i,
						equipmentLat: posi.lat,
						equipmentLng: posi.lon,
					};
					this.requesetData.forEach((item) => {
						if (item.equipmentId === i.equipmentId) {
							this.requesetData.splice(item, 1);
						}
					});
					this.requesetData.push(is);
					return is;
				}
				return { ...i };
			});
		},
		// setDetele() {
		//   this.$Modal.confirm({
		//     title: '提示',
		//     content: '确定要删除吗？',
		//     onOk: () => {
		//       this.tableData = this.tableData.filter(i => {

		//       })
		//     }
		//   })
		// },
		initTree() {
			let url = `/mechanical/equipmentLocation/equipmentTypeList`;
			Util.request(url).then((res) => {
				let data = res.data;
				let result = data.data.map((i) => {
					return {
						...i,
						title: i.name,
					};
				});
				this.$set(this.data, 0, {
					title: "全部",
					expand: true,
					children: result,
				});
			});
		},
		handleCheckChange(item) {
			this.activesItem = item;
			this.names = item.map((i) => i.name).filter((i) => i != undefined);
			if (this.names.length === 0) {
				this.tableData = [];
				this.total = 0;
				this.$bus.emit("setTableData", this.tableData);
				return;
			}
			this.page = {
				current: 1,
				size: 10,
			};
			this.initTable();
		},
		initTable() {
			// 表格数据
			let url = `/mechanical/equipmentLocation/listPage`;
			Util.requestPost(url, {
				page: {
					current: this.page.current,
					size: this.page.size,
				},
				customQueryParams: {
					typeNames: this.names,
					deviceNameOrCode: this.searhhValue,
				},
			}).then((res) => {
				let data = res.data.data.records;
				this.tableData = data;
				this.total = res.data.data.total;
				// this.$bus.emit("setTableData", data, "qjMap");
			});
			// 打点数据
			Util.requestPost(url, {
				page: {
					current: 1,
					size: -1,
				},
				customQueryParams: {
					typeNames: this.names,
					deviceNameOrCode: this.searhhValue,
				},
			}).then((res) => {
				let data = res.data.data.records;
				this.$bus.emit("setTableData", data, "qjMap");
			});
		},
		handleRadioChange(e, row) {
			this.tableData = this.tableData.map((i) => {
				if (i.equipmentId === row.equipmentId) {
					row.equipmentLat = "";
					row.equipmentLng = "";
					return {
						...i,
						locationType: e,
						equipmentLat: "",
						equipmentLng: "",
					};
				}
				return i;
			});
			this.$bus.emit("removePoint", row.equipmentId);
			this.requesetData.forEach((i) => {
				if (i.equipmentId === row.equipmentId) {
					this.requesetData.splice(i, 1);
				}
			});
			this.requesetData.push(row);
		},
		handleClick(item) {
			this.$bus.emit("setPoint", item, "qjMap");
		},
		handleSearch() {
			this.initTable();
		},
		handleReset() {
			this.searhhValue = "";
			this.page = {
				current: 1,
				size: 10,
			};
			this.initTable();
		},
		// handleDetele(row) {
		//   this.$Modal.confirm({
		//     title: '确定要删除吗',
		//     onOk: () => {
		//       Util.requestDelete('/mechanical/equipmentLocation/delete', {
		//           id: row.equipmentId
		//         }).then(res => {
		//           this.$Message.success('删除成功')
		//           this.initTable()
		//         })
		//       }
		//   })
		// },
		handleCurrentChange(val) {
			this.page.current = val;
			this.initTable();
		},
		handleSizeChange(val) {
			this.page.size = val;
			this.initTable();
		},
	},
};
</script>

<style lang="less" scoped>
/deep/ .ivu-card-body {
	height: 100%;
	overflow: hidden;
}
.tree-container {
	box-shadow: 0px 0px 9.9px 0px rgba(83, 117, 167, 0.18);
	border-radius: 4px;
	height: 100%;
	overflow-y: auto;
	padding: 8px;
}
.ivu-col {
	height: 100%;
}
.searchInfo {
	width: 100%;
	height: 100%;
	display: flex;
	gap: 10px;
	flex-direction: column;
	.searchInput {
		display: flex;
		gap: 5px;
		align-items: center;
	}
	.button {
		display: flex;
		gap: 5px;
	}
	.ivu-icon-ios-refresh {
		font-size: 22px;
		line-height: 24px;
		vertical-align: middle;
	}
	.ivu-table-wrapper {
		flex: 1;
		/deep/.ivu-table {
			display: flex;
			flex-direction: column;
			& > div {
				width: 100%;
			}
			.ivu-table-body {
				flex: 1;
				overflow-y: auto;
			}
		}
	}
	.ivu-radio-wrapper {
		margin-right: 0;
	}
}
</style>
