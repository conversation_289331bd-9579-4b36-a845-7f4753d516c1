<template>
	<div>
		<Modal
			title="选择项目"
			v-model.trim="modalStatus"
			width="500"
			class-name="project-warp"
			:styles="{ maxHeight: '80vh', overflowY: 'auto' }"
			:mask-closable="false"
		>

			<CellGroup @on-click="handleSelect">
				<Input search v-model="searchVal" clearable placeholder="搜索项目" style="margin-bottom: 16px;" />
				<Cell
					v-for="(item, index) in list"
					:key="item.id"

					:name="item.linkappUser.username"
					:selected="item.linkappUser.username == username"
					:disabled="type == '2' && item.linkappUser.username == currentUserName"
				>
					<span
						><Icon type="ios-browsers-outline" />{{
							item.platformProjectName
						}}</span
					>
					<Icon
						slot="extra"
						type="md-checkmark-circle-outline"
						color="#3069ee"
						v-if="item.linkappUser.username == username"
					/>
				</Cell>
				<div class="no-data" v-show="list.length === 0">暂无数据</div>
			</CellGroup>
			<div slot="footer" style="text-align:center;">
				<Button
					class="padding-left-30 padding-right-30"
					@click="modalStatus = false"
					>取消
				</Button>
				<Button
					class="padding-left-30 padding-right-30"
					type="primary"
					@click="handleSave"
					:loading="loading"
					>{{ type == 1 ? '登录' : '确认' }}</Button
				>
			</div>
		</Modal>
	</div>
</template>
<script>
import { ModalCallee } from '@/libs/mixins';
import Util from '@/libs/util.js';
export default {
	name: 'projectSelect',
	mixins: [ModalCallee],
	props: {
		projectList: {
			type: Array,
			default: () => {
				return [];
			}
		},
		type: { default: '1' } // 1：登录时选择  2:切换项目时选择
	},
	data() {
		return {
			loading: false,
			username: '',
			userLoginName: '',
			searchVal: ''
		};
	},
	computed: {
		list() {
			return this.projectList.filter(item => {
				if (item.platformProjectName.indexOf(this.searchVal) >= 0) {
					return item
				}
			})
		},
		currentUserName() {
			// 只用 vue-router 的 query
			return this.$route.query.userName || '';
		}
	},
	created() {
		const windowToken = Util.getWindowToken()
		let user = Util.local.get(windowToken + 'user')
		if (user) {
			this.userLoginName = JSON.parse(user).username;
		}
	},
	methods: {
		search(val) {},
		handleSelect(name) {
			if (this.type == '2' && name === this.currentUserName) {
				return
			}
			this.username = name;
		},
		handleSave() {
			if (!this.username) return;
			this.modalStatus = false;
			this.$emit('submit', this.username);
		}
	}
};
</script>
<style lang="less" scoped>
.project-warp {

}
/deep/.ivu-modal-body {
		height: ~'calc(80vh - 200px)';
    	overflow-y: auto;
		max-height: 500px;
		width: ~'calc(100% - 2px)';
	}
/deep/.ivu-cell-group {
	padding: 20px 20px 0;
	.ivu-cell {
		margin-bottom: 20px;
		border-radius: 4px;
		background: rgba(0, 0, 0, 0.05);
		.ivu-cell-main {
			width: 94%;
			.ivu-icon-ios-browsers-outline {
				margin-right: 5px;
			}
			.ivu-cell-title {
				font-weight: 600;
				width: 100%;
			    overflow: hidden;
			    text-overflow: ellipsis;
			    white-space: nowrap;
			}
			.ivu-cell-label {
				color: rgba(0, 0, 0, 0.35);
				padding-left: 18px;
				padding-top: 0;
			}
		}
	}
}
.no-data{
	text-align: center;
	padding: 40px 0;
}
</style>
