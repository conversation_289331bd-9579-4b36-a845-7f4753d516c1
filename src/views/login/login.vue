<template>
  <div class="container" v-show="hode" :style="bgStyle">
    <!-- <div class="loginImg" :style="this.logoStyle"></div>
		<div class="login-text">

    </div>-->
    <!-- <div class="loginImg" :style="logoStyle"></div> -->
    <div class="login-wrapper">
      <!-- 移到左侧的同样大小的div -->
      <div class="box-cont duplicate-box">
        <div class="login-con login-container image-container">
          <img src="@/images/login_center.png" alt="登录图片" class="login-image" />
        </div>
      </div>
      <div class="box-cont">
        <div class="login-con login-container">
          <h2 class="login-title">{{ platformTitle }}</h2>
          <Tabs :animated="true" :value="tabName">
            <!-- 手机号登录 -->
            <TabPane label="手机号登录" name="phone">
              <Form
                :model="phoneLogin"
                :rules="phoneRules"
                ref="phoneForm"
                label-position="left"
                class="demo-ruleForm"
              >
                <Form-item prop="telephone">
                  <Input
                    type="tel"
                    v-model="phoneLogin.telephone"
                    placeholder="输入手机号"
                    :maxlength="13"
                  >
                    <span slot="prepend">
                      <Icon :size="16" type="md-phone-portrait"></Icon>
                    </span>
                  </Input>
                </Form-item>
                <Form-item prop="verifyCode" style="background: #fff;">
                  <CaptchaInput
                    class="no-radius"
                    v-model="phoneLogin.verifyCode"
                    type="login"
                    :phone="phoneLogin.telephone"
                    @on-enter="handleSubmit('phoneForm')"
                  />
                  <span slot="prepend">
                    <Icon :size="16" type="md-phone-portrait"></Icon>
                  </span>
                </Form-item>
                <Form-item>
                  <Button
                    type="primary"
                    long
                    @click="handleSubmit('phoneForm')"
                    :loading="logining"
                  >登录</Button>
                </Form-item>
                <!-- <Form-item >
								<div class="login-bottom" style="margin: auto;">
									<img src="./images/qiye.png" />
									<a :href="url" target="_blank">
										进入企业端
									</a>
								</div>
                </Form-item>-->
              </Form>
            </TabPane>
            <!-- 账户登录 -->
            <TabPane label="账户登录" name="account">
              <Form
                :model="user"
                :rules="rules"
                ref="loginForm"
                label-position="left"
                class="demo-ruleForm"
              >
                <Form-item prop="username">
                  <Input
                    type="text"
                    placeholder="输入账号"
                    v-model="user.username"
                    @on-enter="handleSubmit('loginForm')"
                    :maxlength="50"
                  >
                    <span slot="prepend">
                      <Icon :size="16" type="ios-person"></Icon>
                    </span>
                  </Input>
                </Form-item>
                <Form-item prop="password">
                  <Input
                    type="password"
                    placeholder="输入密码"
                    v-model="user.password"
                    @on-enter="handleSubmit('loginForm')"
                    :maxlength="20"
                  >
                    <span slot="prepend">
                      <Icon :size="14" type="md-lock"></Icon>
                    </span>
                  </Input>
                </Form-item>
                <div>
                  <span>
                    <Checkbox v-model="rememberChecked">记住登录名</Checkbox>
                  </span>
                </div>

                <div style="color: red;" v-show="errorMsg">{{ errorMsg }}</div>
                <FormItem>
                  <Button
                    type="primary"
                    long
                    @click="handleSubmit('loginForm')"
                    :loading="logining"
                  >登录</Button>
                </FormItem>
                <!-- <Form-item>
                  <div class="login-bottom" style="margin: auto;">
                    <img src="./images/qiye.png" />
                    <a :href="url" target="_blank">进入企业端</a>
                  </div>
                </Form-item> -->
                <!-- <span class="fl">其他登录方式</span>
							<span class="wechart fl"></span>
							<span class="qicq fl"></span>
							<span class="zhifubao fl"></span>
                <span class="register fr" @click="toRegister">注册账户</span>-->
              </Form>
            </TabPane>
          </Tabs>
        </div>
	    <!-- <Poptip trigger="hover" transfer @on-popper-show="creatQrCode">
		  <div class="rq-img" id="qrcode" slot="content" ref="qrCodeUrl"></div>
          <div class="rqcode-box"><img src="./images/icon_login_logo.png" class="login-logo">下载施工云 APP</div>
      </Poptip> -->
      </div>
    </div>
    <component
      :is="componentName"
      :project-list="projectList"
      @close-modal="emitCloseModal()"
      @submit="userSelectProject"
    />
  </div>
</template>

<script>
/** 登录方式
 * http://***********:8092/login.html
 * http://localhost:8192/login.html?username=admintest&pwd=linkapp@52hz&autoLogin=true
 * http://***********:8092/login.html?redirect_uri=http://***********:8092/#/guokeClassroom
 */
import QRCode from 'qrcodejs2';
import Util from '@/libs/util';
import * as Encrypt from '@/libs/encrypt'
import CaptchaInput from '@/components/CaptchaInput';
import ProjectSelect from './projectSelect';
import jumpSystem from '@/libs/jumpSystem';
// import defaultLogo from '@/images/logo.png'
import defaultLogo from '@/images/logo_sale.png';
// import defaultBg from '@/images/bg1.jpg'
// import defaultBg from '@/images/bg11.png'
// import defaultBg from '@/images/bg1_bak.png';
import defaultBg from '@/images/loginBg-6M3gT0bz.png';
import { ModalCaller } from '@/libs/mixins';
import config from '@/config/config';
export default {
  components: {
    CaptchaInput,
    ProjectSelect
  },
  mixins: [ModalCaller],
  data() {
    const checkPhone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入手机号'));
      } else {
        var pattern = /^1[3456789]\d{9}$/;
        if (pattern.test(value)) {
          callback();
        } else {
          return callback('请输入正确的手机号');
        }
      }
    };
    return {
      url: '',
      hode: false,
      projectList: [],
      defaultTitle: '营业线非营业线技防管理平台', // 默认平台名字
      host: '', // 当前域名地址，含端口号
      title: '营业线非营业线技防管理平台', // 个性化的平台名称
      bgUrl: '', // 个性化的 背景图片地址
      lightLogoUrl: '', // 个性化的 浅色logo
      deepLogoUrl: '', // 个性化的 深色logo

      logining: false,
      tabName: 'account',
      user: {
        username: '',
        password: ''
        // captcha: ''
      },
      rememberChecked: false,
      phoneLogin: {
        telephone: '',
        verifyCode: ''
      },
      logoImgUrl: '1',
      getBackgroundStyle: {},
      getLoginBackgroundStyle: {},
      displayLogin: false,
      platformName: '',
      rules: {
        username: [
          {
            required: true,
            message: '请输入用户名',
            trigger: 'blur'
          },
          {
            type: 'string',
            min: 4,
            max: 32,
            message: '用户名长度为4-32位',
            trigger: 'blur'
          }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          {
            type: 'string',
            min: 6,
            max: 20,
            message: '密码长度为6-20位',
            trigger: 'blur'
          }
        ]
      },
      phoneRules: {
        telephone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          },
          { validator: checkPhone, trigger: 'blur' }
        ],
        verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      },
      errorMsg: ''
    };
  },
  computed: {
    logoStyle() {
      // let tmpUrl = (this.lightLogoUrl && this.lightLogoUrl.length>0)?this.lightLogoUrl:defaultLogo
      let tmpUrl = this.deepLogoUrl && this.deepLogoUrl.length > 0 ? this.deepLogoUrl : defaultLogo;
      return {
        backgroundImage: 'url(' + tmpUrl + ')'
      };
    },
    bgStyle() {
      let tmpUrl = this.bgUrl && this.bgUrl.length > 0 ? this.bgUrl : defaultBg;
      return {
        backgroundImage: 'url(' + tmpUrl + ')'
      };
    },
    platformTitle() {
      // console.log('切换标题5555',this.title);
      return Util.isEmpty(this.title) ? this.defaultTitle : this.title;
    }
  },
  created() {
    if (window) {
      if (window.location.href.indexOf('loginDevice=app') > -1) {
        localStorage.setItem('location_search', location.search);
        this.user.username = localStorage.getItem('d_username')
          ? localStorage.getItem('d_username')
          : '';
        this.user.password = localStorage.getItem('d_password')
          ? localStorage.getItem('d_password')
          : '';
      } else {
        localStorage.removeItem('location_search');
        localStorage.removeItem('d_username');
        localStorage.removeItem('d_password');
      }
      Util.request('/config/getEduUrl', {}).then((res) => {
        if (res.data.success) {
          this.url = res.data.data;
        }
      });
      this.host = window.location.host;
      //   Util.request('https://www.fastmock.site/mock/6164a60f0631f4094f807b0c22eea674/agriculture/api/getCustomization',
      Util.request(
        '/personality/getPersonalIfo',
        {
          domin: this.host
        },
        'post',
        {},
        {},
        false
      )
        .then((res) => {
          if (res.data.success) {
            if (res.data.data && this.host == res.data.data.domin) {
              this.title = res.data.data.platform;
              this.bgUrl = res.data.data.loginBack;
              this.lightLogoUrl = res.data.data.lightColorLogo;
              this.deepLogoUrl = res.data.data.deepColorLogo;
              let customization = {
                host: this.host,
                title: this.title,
                bgUrl: this.bgUrl,
                lightLogoUrl: this.lightLogoUrl,
                deepLogoUrl: this.deepLogoUrl
              };
              Util.local.set('customization', JSON.stringify(customization));
              Util.local.set('platformTitle', this.title);
            } else {
              Util.local.removeItem('customization');
              Util.local.set('platformTitle', this.title);
            }
            // 设置title
            Util.title();
            // document.title = this.platformTitle?this.platformTitle:this.title;
          }
          this.hode = true;
        })
        .catch((err) => {
          this.hode = true;
        });
    } else {
      this.hode = true;
    }
  },
  mounted() {
    this.autoLogin();
	let rememberUsername = localStorage.getItem('rememberUsername')
	if (rememberUsername) {
		this.user.username = rememberUsername
		this.rememberChecked = true;
	}
  },
  methods: {
	creatQrCode() {
		document.getElementById('qrcode').innerHTML = '';
      	let qrcode = new QRCode(this.$refs.qrCodeUrl, {
        text: `${window.location.origin}/constructionapph5/#/downloadApp`, // 需要转换为二维码的内容
        width: 120,
        height: 120,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.L
      });
	},
    autoLogin() {
      const username = this.getURLParameter('username');
      const pwd = this.getURLParameter('pwd');
      const autoLogin = this.getURLParameter('autoLogin');
      if (username && pwd) {
        this.user.username = username;
        this.user.password = pwd;
        const user = Util.getUser();
        if (!user.username) {
          if (autoLogin == 'true') {
            this.usernameLogin();
          }
        } else if (user.username) {
          Util.request('/logout', {}, 'post').then((resp) => {
            // 退出要清除缓存
            Util.local.clearSession();
            if (autoLogin == 'true') {
              this.usernameLogin();
            }
          });
        }
      }
    },
    handleSubmit(name) {
      this.logining = true;
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.user.username = this.user.username.trim();
          if (name == 'loginForm') {
            if (this.rememberChecked) {
              localStorage.setItem('rememberUsername', this.user.username);
            } else {
              localStorage.removeItem('rememberUsername');
            }
            this.usernameLogin();
          } else {
            this.phonenumberLogin();
          }
          if (window.location.href.indexOf('loginDevice=app') > -1) {
            localStorage.setItem('d_username', this.user.username);
            localStorage.setItem('d_password', this.user.password);
          }
        } else {
          this.logining = false;
        }
      });
    },
    usernameLogin() {
      this.logining = true;
      this.errorMsg = '';
      let params = {
        username: this.user.username,
        password: Encrypt.encryptedData(this.user.password)
      }
      Util.request(
        '/login?username=' +
          encodeURIComponent(params.username) +
          '&phone=' +
          encodeURIComponent(params.username) +
          '&password=' +
          encodeURIComponent(params.password),
          params,
        'post',
        null,
        null,
        false
      )
        .then((resp) => {
          if (resp.data.success) {
            Util.local.set('isLogin', 1);
            console.log('当前的环境', config.env);
            this.jumpBigScreen();
            // this.getUserProject();
          } else {
            if (resp.data.message == 'Bad credentials') {
              this.errorMsg = '账号或密码不正确';
            } else {
              this.errorMsg = resp.data.message;
            }
            this.logining = false;
          }
        })
        .finally(() => {
          this.logining = false;
        });
    },
    phonenumberLogin() {
      this.logining = true;
      Util.request(
        '/phoneLogin?phone=' +
          this.phoneLogin.telephone +
          '&verificationCode=' +
          this.phoneLogin.verifyCode,
        '',
        'post'
      )
        .then((resp) => {
          if (resp.data.success) {
            Util.local.set('isLogin', 1);
            this.jumpBigScreen()
            // this.getUserProject();
          } else {
            this.$Modal.warning({
              title: '登录失败。',
              // content: `详细信息：${resp.data.message}`
              content: '您输入的手机号暂未绑定相关账号'
            });
            this.logining = false;
          }
        })
        .finally(() => {
          this.logining = false;
        });
    },
    // 忘记密码
    forgotPassWord() {},
    // 去注册账户
    toRegister() {},
    loginSuccess(username) {
      // 设置请求头，包含windowToken
      let headers = {
        Pragma: 'no-cache', 
        'Cache-Control': 'no-cache'
      };
      
      // 如果有传入的username参数，设置为windowToken
      if (username) {
        headers['windowToken'] = decodeURIComponent(username);
      }
      
      Util.request(
        '/login/info',
        {},
        'get',
        headers,
        {},
        false
      ).then((resp) => {
        let respData = resp.data;
        if (!(respData.success && respData.data.user)) {
          Util.local.clearSession();
          // this.$router.push('/login')
        } else {
          debugger
          const data = respData.data;
          const authData = respData.data.auth;
          let auth = {};
          authData.forEach((item) => {
            auth[item.code] = true;
          });
          const windowToken = data.user.username
          Util.local.set(windowToken + 'auth', JSON.stringify(auth || '{}'));
          // 设置 user
          // 如果路径中没有 userName，则从 data.user 里获取用户名并拼接到当前 URL
          const urlParams = new URLSearchParams(window.location.search);
          urlParams.set('userName', encodeURIComponent(data.user.username));
          const newUrl = window.location.origin + window.location.pathname + '?' + urlParams.toString();
          window.history.replaceState(null, '', newUrl);
          Util.local.set(windowToken+'user', JSON.stringify(data.user));
          if (window.location.href.split('?')[1] && window.location.href.split('?')[1].length > 0) {
            let queryList = window.location.href.split('?')[1].split('&');
            let f = queryList.some((item) => {
              if (item.indexOf('redirect_uri=') > -1) {
                Util.jumpWithUserName(item.replace('redirect_uri=', ''),username);
              }
              return item.indexOf('redirect_uri=') > -1;
            });
            if (f) return;
          }
          if (data.user.indexConfigUrl) {
            Util.jumpDefaultPage(data.user);
          } else {
            Util.request('/linkappPrivilege/selectPrivilegeByUser', {}, 'get').then((res) => {
              if (res.data.success) {
                let url = '/welcome';
                if (res.data.data.length) {
                  for (let item of res.data.data) {
                    if (item.url) {
                      url = item.url;
                      break;
                    }
                  }
                }
                let hash = '/#';
                if (!url.startsWith('/')) {
                  hash += '/';
                }
                Util.jumpWithUserName(hash + url,username);
              }
            });
          }
        }
      });
    },
    getURLParameter(sParam) {
      const url = location.href;
      let sPageURL = url.substring(url.indexOf('?') + 1);
      let sURLVariables = sPageURL.split('&');
      for (let i = 0; i < sURLVariables.length; i++) {
        let sParameterName = sURLVariables[i].split('=');
        if (sParameterName[0] == sParam) {
          return sParameterName[1];
        }
      }
    },
    // 获取当前用户的项目
    getUserProject() {
      this.logining = true;
      Util.request('/login/getUserProject', {}, 'get')
        .then((res) => {
          if (res.data.success) {
            const { projectList = [] } = res.data.data;
            if (projectList.length == 1) {
              // const { linkappUser = {} } = projectList[0].linkappUser;
              // this.userSelectProject(linkappUser.username);
              this.projectList = projectList
              const linkappUser = projectList[0].linkappUser || {};
              this.userSelectProject(linkappUser.username);
            } else if (projectList.length > 1) {
              this.projectList = projectList;
              this.componentName = 'ProjectSelect';
            }
          }
        })
        .finally(() => (this.logining = false));
    },
    // 选择项目登录
    userSelectProject(username) {
      if (!username) return;
      this.logining = true;

      // 将当前项目名存入缓存
      this.projectList.forEach((item, index) => {
        if (item.linkappUser.username == username) {
          const windowToken = Util.getWindowToken()
          Util.local.set(windowToken + 'projectName', item.platformProjectName);
          Util.local.set(windowToken + 'projectId', item.projectId);
        }
      });
      Util.request('/login/userSelectProject', { username }, 'post')
        .then((res) => {
          if (res.data.success) {
            // 切换项目后，更新URL上的userName参数
            const urlParams = new URLSearchParams(window.location.search);
            urlParams.set('userName', encodeURIComponent(username));
            const newUrl = window.location.origin + window.location.pathname + '?' + urlParams.toString() + window.location.hash;
            window.history.replaceState(null, '', newUrl);
            this.loginSuccess(username);
          }
        })
        .finally(() => (this.logining = false));
    },
    jumpBigScreen() {
      // 使用动态跳转系统
      jumpSystem.jump();
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  position: relative;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  min-width: 950px;
  overflow: auto;
  background-image: url(../../images/bg1.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  padding: 0px;
  margin: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  .loginImg {
    /* position: absolute; */
    /* top: 100px; */
    /* left: 100px; */
    /* width: 200px; */
    width: 13.75vw;
    height: 3.78vh;
    margin-bottom: 15px;
    // transform: translateY(-60%);
    //   background-image: url(../images/logo.png);
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    position: absolute;
    left: 7.63vw;
    top: 7.63vh;
  }
  .login-text {
    position: absolute;
    top: 260px;
    left: 100px;
    width: 370px;
    color: #fff;
    .text-cn {
      font-size: 58px;
    }
    .text-en {
      font-size: 26px;
    }
  }
  .login-con {
    // position: absolute;
    // right: 160px;
    // top: 0%;
    // transform: translateY(-60%);
    width: 600px;
  }
  .login-wrapper {
    display: flex;
    gap: 0;
    align-items: stretch;
  }
  .box-cont {
    display: flex;
    height: auto;
    align-items: center;
    width: 600px;
    flex-direction: column;
    justify-content: center;
	.rqcode-box {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 4px;
		width: 200px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		.login-logo {
			display: block;
			height: 20px;
			width: 20px;
			margin-right: 10px;
		}

	}
  }
  .login-container {
    /*box-shadow: 0 0px 8px 0 rgba(0, 0, 0, 0.06), 0 1px 0px 0 rgba(0, 0, 0, 0.02);*/
    /* -webkit-border-radius: 5px; */
    /* border-radius: 5px; */
    /* -moz-border-radius: 5px; */
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.24);
    opacity: 1;
    border-radius: 4px;
    background-clip: padding-box;
    width: 600px;
    height: 458px;
    padding: 25px 35px 15px 35px;
    background: #fffb;
    border: 1px solid #eaeaea88;
	margin-bottom: 40px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .login-title {
      margin: 0px auto 25px auto;
      text-align: center;
      /* color: #505458; */
      color: #2d8cf0;
      font-size: 28px;
      font-weight: 600;
      line-height: 30px;
    }
    .ivu-form-item {
      line-height: 48px;
      margin-bottom: 20px;
    }
    .login-container .ivu-input {
      height: 48px !important;
      line-height: 48px !important;
    }
    .login-container .ivu-input-default {
      height: 48px !important;
      line-height: 48px !important;
    }
    .login-container .ivu-input input {
      height: 48px !important;
      line-height: 48px !important;
    }
    .login-container .ivu-input-default input {
      height: 48px !important;
      line-height: 48px !important;
    }
    .login-container .ivu-input-group {
      height: 48px !important;
    }
    .login-container .ivu-input-group .ivu-input {
      height: 48px !important;
      line-height: 48px !important;
    }
    .login-container .no-radius {
      height: 48px !important;
    }
    .login-container .no-radius input {
      height: 48px !important;
      line-height: 48px !important;
    }
    .login-container .ivu-btn {
      height: 48px !important;
      line-height: 48px !important;
    }
    .login-container .ivu-btn-primary {
      height: 48px !important;
      line-height: 48px !important;
    }
    .login-container .ivu-checkbox-wrapper {
      font-size: 16px;
      font-weight: 400;
    }
    .login-container .ivu-checkbox-wrapper {
      margin-bottom: 20px;
      display: block;
    }
    .login-bottom {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 1);
      width: 15vh;
      height: 2.96vh;
      border-radius: 3.61vh;
      img {
        width: 1.851vh;
        height: 1.851vh;
        background-size: 100% 100%;
        margin-right: 0.851vh;
      }
      a {
        color: #24cc83;
        font-family: "PingFang SC";
        font-size: 1.29vh;
      }
    }
    .wechart {
      cursor: pointer;
    }
    .register {
      cursor: pointer;
      color: #2d8cf0;
    }
  }
  .duplicate-box {
    .login-container {
      height: 458px;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
    }
    .image-container {
      padding: 0;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .login-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }
}

.fl {
  float: left;
}
.fr {
  float: right;
}
</style>

<style lang="less">
/* 基本的tabs样式 */
.login-container .ivu-tabs-nav {
  width: 100%;
}
.login-container .ivu-tabs-tab {
  text-align: center;
  width: 50%;
  font-size: 18px;
  font-weight: 500;
}

/* 强制设置输入框高度 */
.login-container .ivu-input,
.login-container .ivu-input-default {
  height: 48px !important;
  line-height: 48px !important;
}

.login-container .ivu-input input,
.login-container .ivu-input-default input {
  height: 48px !important;
  line-height: 48px !important;
  padding: 0 7px !important;
}

.login-container .ivu-btn-primary {
  height: 48px !important;
  line-height: 48px !important;
}
</style>

