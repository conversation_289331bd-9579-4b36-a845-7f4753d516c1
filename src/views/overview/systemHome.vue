<style lang="less" scoped>
/deep/.ivu-avatar {
  border-radius: 6px;
}
/deep/.iconfont:before {
  color: #bfc7d4;
}
/deep/.ivu-card-head {
  padding-bottom: 0;
  border-bottom: none;
}
.deviceNumCard {
  border-radius: 12px;
  box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);

  .deviceNumCard-icon {
    width: 60%;
    min-width: 50px;
    max-width: 80px;
  }
}

// 菜单数据加载状态样式
.menu-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.menu-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  .spin-icon-load {
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
    color: #0062ff;
  }
  
  span {
    font-size: 14px;
    color: #666;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.overview-jg {
  .mb-16 {
    margin-bottom: 16px;
  }
  .txt-color {
    color: #999;
  }
  .overOne {
    white-space: nowrap;

    overflow: hidden;

    text-overflow: ellipsis;
  }
  .bd-b {
    border-bottom: 1px solid #eee;
  }
  .bd-r {
    border-right: 1px solid #eee;
    &:hover {
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.09);
      border: 1px solid #3868f6;
    }
  }
  .user-info {
    display: flex;
    .info {
      flex: 1;
      margin: 0 15px;
      .name {
        font-size: 16px;
        line-height: 30px;
      }
    }
    .box {
      position: relative;
      padding: 0 30px;
      text-align: right;
      line-height: 24px;
      .name {
      }
      .num {
        font-size: 16px;
      }
      &.line:after {
        content: "";
        position: absolute;
        width: 1px;
        height: 70%;
        background: #eee;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
      }
    }
  }
  .fast-guid {
    .box {
      padding: 15px;
      .ftop {
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        &:hover {
          /deep/.iconfont:before {
            color: #0062ff;
          }
          /deep/.name {
            color: #0062ff;
          }
        }
        .img {
          width: 32px;
          height: 32px;
          margin-bottom: 10px;
          border-radius: 50%;
          background: #f2f5fa;
        }
        .name {
          font-size: 1.2vh;
          color: #17233d;
        }
      }
      .txt {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        line-height: 24px;
        height: 72px;
        font-size: 12px;
      }
    }
  }
  .dynamics-list {
    .box {
      display: flex;
      padding: 10px 0;
      .right {
        flex: 1;
        margin-left: 10px;
        .dtop {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10px;
        }
        .name {
          font-weight: 600;
        }
        .time {
        }
        .txt {
          line-height: 20px;
        }
      }
    }
  }
  .headCard {
    display: flex;
    .headLeft {
      width: 15vh;
      height: 15vh;
      margin-right: 3vh;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .headMain {
      flex: 1;
      .title {
        letter-spacing: 0.116667px;
        font-weight: 600;
        font-size: 3vh;
        color: #17233d;
        padding-left: 0;
      }
      .subs {
        display: flex;
        flex-wrap: wrap;
        .sub {
          min-width: 33.3%;
          max-width: 33.3%;
          display: flex;
          padding: 0.4vh 0;
          .name {
            font-weight: 400;
            font-size: 1.6vh;
            padding-right: 2vh;
            color: #9a9ea7;
          }
          .msg {
            font-weight: 500;
            font-size: 1.8vh;
            color: #4e5969;
          }
        }
      }
    }
  }
  .headCenter {
    // background: red;
    display: flex;
    align-items: center;
    justify-content: center;
    .xian {
      height: 70%;
      width: 1px;
      background: #e0e5ed;
    }
  }
  .headRight {
    display: flex;
    flex-direction: column;
    .top {
      .topBox {
        background: #cfe2ff;
        border-radius: 4px;
        min-height: 4vh;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #1f55d2;
        font-size: 1.8vh;
        .left {
        }
        .center {
          min-width: 30%;
          text-align: center;
          padding: 0 1vh;
        }
      }
    }
    .body {
      display: flex;
      justify-content: center;

      /deep/.ivu-chart-circle {
        margin-top: 2vh;
        width: 11vh !important;
        height: 11vh !important;
        .demo-i-circle-custom {
          display: flex;
          align-items: center;
          justify-content: center;
          & .iconWrap {
            width: 3vh;
            height: 3vh;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
      .msgs {
        padding-left: 4vh;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        height: 100%;
        .num {
          font-weight: 600;
          font-size: 2.9vh;
          line-height: 3.8vh;
          /* identical to box height */
          letter-spacing: 0.1px;
          color: #142a4a;
        }
        .msg {
          font-weight: 400;
          font-size: 1.6vh;
          line-height: 2vh;
          letter-spacing: 0.1px;
          color: #696974;
        }
      }
    }
  }
}
</style>

<template>
  <div class="overview-jg">
    <!-- 菜单数据加载状态显示 -->
    <div v-if="!menuDataReady" class="menu-loading-overlay">
      <div class="menu-loading-content">
        <Icon type="ios-loading" size="24" class="spin-icon-load" />
        <span>正在加载菜单数据...</span>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div v-if="menuDataReady">
      <Card dis-hover class="mb-16">
        <Row :gutter="0">
          <Col span="18" class="headCard">
            <div class="headLeft">
              <img src="./images/xm_icon.png" alt />
            </div>
            <div class="headMain">
              <div class="name title title-color" style="min-height:8vh">
                <span :style="{color:titleColor}">{{project.platformProjectName}}</span>
              </div>
              <div class="subs">
                <div class="sub">
                  <div class="name">预计开工日期</div>
                  <div class="msg" :style="{color:titleColor}">{{getTime(project.estimateTime)}}</div>
                </div>
                <!-- <div class="sub">
                <div class="name">项目类型</div>
                <div class="msg" :style="{color:titleColor}" v-if="project.projectType==0">房屋建筑</div>
                <div class="msg" :style="{color:titleColor}" v-else-if="project.projectType==1">市政工程</div>
                <div
                  class="msg"
                  :style="{color:titleColor}"
                  v-else-if="project.projectType==2"
                >建筑智能化</div>
                <div class="msg" :style="{color:titleColor}" v-else-if="project.projectType==3">其他</div>
                <div class="msg" :style="{color:titleColor}" v-else>--</div>
              </div> -->
              <!-- <div class="sub">
                <div class="name">建筑面积</div>
                <div class="msg" :style="{color:titleColor}">{{project.area}}m²</div>
              </div> -->
              <div class="sub">
                <div class="name">预计竣工日期</div>
                <div class="msg" :style="{color:titleColor}">{{getTime(project.completionTime)}}</div>
              </div>
              <div class="sub">
                <div class="name">涉铁类别</div>
                <div class="msg" :style="{color:titleColor}" v-if="project.projectCrossType==1">上跨</div>
                <div class="msg" :style="{color:titleColor}" v-else-if="project.projectCrossType==2">下穿</div>
                <div class="msg" :style="{color:titleColor}" v-else-if="project.projectCrossType==3">临近</div>
              </div>
              <div class="sub">
                <div class="name">涉及线别</div>
                <div class="msg" :style="{color:titleColor}">{{project.projectLine}}</div>
              </div>
              <div class="sub">
                <div class="name">影响运输</div>
                <div class="msg" :style="{color:titleColor}">{{project.projectTransport}}</div>
              </div>
              <div class="sub">
                <div class="name">区间及里程</div>
                <div class="msg" :style="{color:titleColor}">{{project.projectIntervalDistance}}</div>
              </div>
              <!-- <div class="sub">
                <div class="name">创优目标</div>
                <div class="msg" :style="{color:titleColor}">
                  <span v-if="project.qualityExcellenceLevel!=null">
                    质量创优
                    <span v-if="project.qualityExcellenceLevel==0">国家级</span>
                    <span v-else-if="project.qualityExcellenceLevel==1">省级</span>
                    <span v-else-if="project.qualityExcellenceLevel==2">市级</span>、
                  </span>
                  <span v-if="project.safetyExcellenceLevel!=null">
                    安全创优
                    <span v-if="project.safetyExcellenceLevel==0">国家级</span>
                    <span v-else-if="project.safetyExcellenceLevel==1">省级</span>
                    <span v-else-if="project.safetyExcellenceLevel==2">市级</span>
                  </span>
                </div>
              </div> -->
            </div>
          </div>
        </Col>
        <Col span="1" class="headCenter">
          <div class="xian"></div>
        </Col>
        <Col span="5" class="headRight">
          <div class="top">
            <div class="topBox">
              <div class="left" v-if="project.surplusDay>=0">剩余</div>
              <div class="left" v-else>已延期</div>
              <div class="center">{{Math.abs(project.surplusDay)}}天</div>
              <div class="left" v-if="project.surplusDay>=0">竣工</div>
            </div>
          </div>
          <div class="body">
            <i-circle
              :percent="((project.progress || 0) * 100 > 100 ? 100 : (project.progress || 0) * 100)"
              stroke-linecap="square"
              stroke-color="#056dff"
            >
              <div class="demo-i-circle-custom">
                <div class="iconWrap">
                  <img src="./images/ic_Goal.png" alt />
                </div>
              </div>
            </i-circle>
            <div class="msgs">
              <div
                class="num"
                :style="{color:titleColor}"
              >{{ ((project.progress || 0) * 100 > 100 ? 100 : (project.progress || 0) * 100).toFixed(2) }}%</div>
              <div class="msg">项目已完成</div>
            </div>
          </div>
        </Col>
      </Row>
    </Card>

    <Row :gutter="16">
      <Col span="18">
        <Card dis-hover class="mb-16" title="项目简介">
          <div class="info">
            <div class="txt txt-color">{{project.projectDesc}}</div>
          </div>
        </Card>

        <Card dis-hover class="mb-16">
          <p slot="title" style="display:flex;justify-content: space-between;">
            <span>最新动态</span>
            <span
              style="color: #165DFF;cursor: pointer;font-size:1.4vh"
              @click="toLink('/manage/messageCenter')"
            >查看更多</span>
          </p>
          <div class="dynamics-list">
            <!-- 暂无数据 -->
            <div class="box bd-b" v-for="(n,i) in this.newDynamicList" :key="i">
              <!-- <Avatar style="background-color: rgb(48 105 238)" icon="ios-person" /> -->
              <div style="min-width:7vh">
                <Tag color="red" v-if="n.type==3">告警</Tag>
                <Tag color="blue" v-else>通知</Tag>
              </div>
              <div class="right" style="width:90%">
                <div class="dtop">
                  <div class="name title-color">{{n.title}}</div>
                  <div class="time txt-color">{{n.createTime}}</div>
                </div>
                <div class="txt txt-color overOne">{{n.content}}</div>
              </div>
            </div>
          </div>
        </Card>
      </Col>
      <Col span="6" :gutter="10">
        <WatherCard
          :weatherData="project.weatherData.info"
          :area="project.weatherData.area"
          v-if="project.weatherData"
        ></WatherCard>
        <Card dis-hover title="快捷入口" :padding="0">
          <Row :wrap="true" class="fast-guid">
            <Col span="8" class="box" v-if='checkAuthByPerantAuthCode("labourServices")'>
              <div @click='toFirstChildLink("labourServices")'>
                <div class="ftop">
                  <div class="img">
                    <Avatar
                      style="background-color: rgb(242, 245, 250)"
                      class="iconfont icon-kaoqin"
                    />
                  </div>
                  <div class="name" :style="{color:titleColor}">人员管理</div>
                </div>
              </div>
            </Col>
            <Col span="8" class="box" v-if='checkAuthByPerantAuthCode("environmentalMonitoring")'>
              <div @click='toFirstChildLink("environmentalMonitoring")'>
                <div class="ftop">
                  <div class="img">
                    <Avatar
                      style="background-color: rgb(242, 245, 250)"
                      class="iconfont icon-huanjing"
                    />
                  </div>
                  <div class="name" :style="{color:titleColor}">环境管理</div>
                </div>
              </div>
            </Col>
            <Col span="8" class="box" v-if='checkAuthByPerantAuthCode("machineryManagement")'>
              <div @click='toFirstChildLink("machineryManagement")'>
                <div class="ftop">
                  <div class="img">
                    <Avatar
                      style="background-color: rgb(242, 245, 250)"
                      class="iconfont icon-guize"
                    />
                  </div>
                  <div class="name" :style="{color:titleColor}">机械管理</div>
                </div>
              </div>
            </Col>
            <Col span="8" class="box" v-if='checkAuthByPerantAuthCode("videoMonitor")'>
              <div @click='toFirstChildLink("videoMonitor")'>
                <div class="ftop">
                  <div class="img">
                    <Avatar
                      style="background-color: rgb(242, 245, 250)"
                      class="iconfont icon-jiankong_shipinjiankong"
                    />
                  </div>
                  <div class="name" :style="{color:titleColor}">视频监控</div>
                </div>
              </div>
            </Col>
            <Col span="8" class="box" v-if='checkAuthByPerantAuthCode("security")'>
              <div @click='toFirstChildLink("security")'>
                <div class="ftop">
                  <div class="img">
                    <Avatar
                      style="background-color: rgb(242, 245, 250)"
                      class="iconfont icon-decheng_zhihuijiaoshi"
                    />
                  </div>
                  <div class="name" :style="{color:titleColor}">安全管理</div>
                </div>
              </div>
            </Col>
            <Col span="8" class="box" v-if='checkAuthByPerantAuthCode("qualityManagement")'>
              <div @click='toFirstChildLink("qualityManagement")'>
                <div class="ftop">
                  <div class="img">
                    <Avatar
                      style="background-color: rgb(242, 245, 250)"
                      class="iconfont icon-guize"
                    />
                  </div>
                  <div class="name" :style="{color:titleColor}">质量管理</div>
                </div>
              </div>
            </Col>
          </Row>
        </Card>
        <Card dis-hover title="最近访问" :padding="0" class="mb-16">
          <Row :wrap="true" class="fast-guid">
            <Col span="8" class="box" v-for="(item,index) in latelyList" :key="index">
              <div @click="toLink(item.url)">
                <div class="ftop">
                  <div class="img">
                    <Avatar
                      style="background-color: rgb(242, 245, 250)"
                      class="iconfont icon-xitongguanli"
                    />
                  </div>
                  <div class="name" :style="{color:titleColor}">{{item.name}}</div>
                </div>
              </div>
            </Col>
          </Row>
        </Card>
        <!-- <WatherCard></WatherCard> -->
        <!-- <Card dis-hover class="device-info" title="隐患趋势">
        <RadioGroup v-model="trendType" @on-change="changeTrendType" type="button">
            <Radio :label="1">安全隐患</Radio>
            <Radio :label="2">质量隐患</Radio>
          </RadioGroup>
          <div @mouseover="deviceTypeChartSeover" @mouseout="deviceTypeChartOut">
            <v-chart
              :options="deviceTypeChartOption"
              ref="deviceTypeChart"
              style=" width: 100%;"
              :autoresize="true"
            ></v-chart>
        </div>
        </Card>-->
      </Col>
    </Row>
    </div>
  </div>
</template>

<script>
import Util from "@/libs/util.js";
import ECharts from "@/components/ECharts";
import alarmRanking from "./alarmRanking";
import WatherCard from "./watherCard";
export default {
  components: {
    "v-chart": ECharts,
    WatherCard,
    alarmRanking,
  },
  data() {
    const deviceTypeChartOption = {
      color: [
        "#FE2C6D",
        "#FA9C1F",
        "#3A5BE8",
        "#936AFF",
        "#FF7B43",
        "#FE4443",
        "#FFD128",
        "#2697FE",
        "#17DBF6",
        "#1BE9AA",
      ],
      tooltip: {
        appendToBody: true,
        trigger: "item",
        formatter: "{a} <br/>{b}: {c} ({d}%)",
      },
      legend: {
        type: "scroll",
        orient: "vertical",
        top: 10,
        right: 10,
        data: ["默认类型"],
        itemWidth: 10,
        itemHeight: 10,
        icon: "circle",
      },
      series: [
        {
          name: "类别",
          type: "pie",
          hoverOffset: 2,
          radius: ["50%", "60%"],
          center: ["35%", "50%"],
          label: {
            show: false,
            position: "center",
            formatter: ["{per|{d}%}", "{def|{b}}", "{def|({c})个}"].join("\n"),
            rich: {
              per: {
                fontSize: 24,
                fontWeight: "bold",
                lineHeight: 30,
              },
              def: {
                lineHeight: 18,
              },
            },
          },
          emphasis: {
            label: {
              show: true,
              fontSize: "14",
              fontWeight: "bold",
            },
          },
          labelLine: {
            show: false,
          },
          // data: pieData
          data: [],
        },
      ],
    };
    const deviceChartOption = {
      title: {
        // text: '设备数情况',
        left: "4%",
        top: "4%",
        textStyle: {
          fontWeight: "light",
          fontSize: 14,
        },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          lineStyle: {
            color: "#444",
          },
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      yAxis: [
        {
          type: "category",
          boundaryGap: true,
          axisTick: { show: false },
          axisLine: { show: true },
          data: [],
        },
      ],
      xAxis: [
        {
          type: "value",
          axisTick: { show: false },
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: "#aaa7",
            },
          },
        },
      ],
      series: [
        {
          name: "设备数",
          type: "bar",
          symbol: "circle",
          symbolSize: 5,
          showSymbol: true,
          barMaxWidth: 10,
          lineStyle: {
            show: false,
            normal: {
              width: 1,
            },
          },
          showBackground: true,
          areaStyle: {
            normal: {
              color: "#2680ebaa",
              shadowColor: "rgba(0, 0, 0, 0.1)",
              shadowBlur: 10,
            },
          },
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: "#2790DC" },
                { offset: 1, color: "#12E7F9" },
              ],
            },
          },
          data: [0],
        },
      ],
    };
    const deviceOnlineChartOption = {
      title: {
        // text: '在线/离线情况',
        left: "4%",
        top: "4%",
        textStyle: {
          fontWeight: "light",
          fontSize: 14,
        },
      },
      legend: {
        top: "4%",
        width: "60%",
        right: "2%",
        itemWidth: 8,
        itemHeight: 8,
        icon: "circle",
        data: [
          {
            name: "离线",
            itemStyle: {
              color: "#FE80A9",
              borderColor: "#FD3978",
              borderWidth: 2,
              shadowColor: "rgba(253,57,120,1)",
              shadowBlur: 10,
            },
          },
          {
            name: "在线",
            itemStyle: {
              color: "#6095FF",
              borderColor: "#0072FE",
              borderWidth: 2,
              shadowColor: "rgba(0,114,254,1)",
              shadowBlur: 10,
            },
          },
        ],
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          lineStyle: {
            color: "#444",
          },
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: [
        {
          type: "category",
          boundaryGap: false,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
          },
          axisLabel: {
            formatter: function (value, index) {
              let i = value.indexOf("-");
              return value.slice(i + 1);
            },
          },
          data: [],
        },
      ],
      yAxis: [
        {
          type: "value",
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: "离线",
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 5,
          showSymbol: false,
          lineStyle: {
            normal: {
              width: 3,
            },
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "rgba(253,57,120, 0.5)" },
                { offset: 1, color: "rgba(253,57,120, 0)" },
              ],
            },
          },
          itemStyle: {
            normal: {
              color: "#FD3978",
            },
            emphasis: {
              color: "#FFB4CC",
              borderColor: "#FD3978",
              borderWidth: 1,
              shadowColor: "rgba(253,57,120, 1)",
              shadowBlur: 10,
            },
          },
          data: [0],
        },
        {
          name: "在线",
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 5,
          showSymbol: false,
          lineStyle: {
            normal: {
              width: 3,
            },
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "rgba(0,114,254, 0.5)" },
                { offset: 1, color: "rgba(0,114,254, 0)" },
              ],
            },
          },
          itemStyle: {
            normal: {
              color: "#0072FE",
            },
            emphasis: {
              color: "#6095FF",
              borderColor: "#0072FE",
              borderWidth: 1,
              shadowColor: "rgba(0,114,254, 1)",
              shadowBlur: 10,
            },
          },
          data: [0],
        },
      ],
    };

    const alarmTypeChartOption = {
      title: {
        text: "告警排名",
        left: "4%",
        top: "2%",
        textStyle: {
          fontWeight: "light",
          fontSize: 14,
        },
      },
      tooltip: {
        show: true,
        formatter: "{b}:{c}",
      },
      grid: {
        left: "1%",
        top: "10%",
        right: "3%",
        bottom: "1%",
        containLabel: true,
      },
      xAxis: {
        type: "value",
        show: false,
        position: "bottom",
      },
      yAxis: [
        {
          // show:false,
          type: "category",
          axisTick: {
            show: false,
            alignWithLabel: false,
            length: 5,
          },
          splitLine: {
            // 网格线
            show: false,
          },
          inverse: "true", // 排序
          axisLine: {
            show: false,
            lineStyle: {
              color: "#555",
            },
          },
          data: [],
        },
      ],
      series: [
        {
          type: "bar",
          barMaxWidth: 10,
          showBackground: true,
          label: {
            normal: {
              show: true,
              position: "right",
              formatter: "{c}",
              textStyle: {
                color: "#333", // color of value
              },
            },
          },
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: "#FA2E5D" },
                { offset: 1, color: "#EC2F77" },
              ],
            },
          },
          barCategoryGap: "60%",
          data: [],
        },
      ],
    };
    const alarmHandleChartOption = {
      title: {
        // text: '告警处理情况',
        left: "4%",
        top: "2%",
        textStyle: {
          fontWeight: "light",
          fontSize: 14,
        },
      },
      legend: {
        top: "12px",
        width: "60%",
        right: "2%",
        textStyle: {
          color: "#555",
          borderColor: "#555",
        },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          lineStyle: {
            color: "#444",
          },
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: [
        {
          type: "category",
          boundaryGap: true,
          axisLine: {},
          data: [],
        },
      ],
      yAxis: [
        {
          type: "value",
          axisTick: {
            show: false,
          },
          axisLine: {},
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: "未处理",
          type: "bar",
          smooth: true,
          barMaxWidth: 10,
          symbol: "circle",
          symbolSize: 5,
          showSymbol: false,
          lineStyle: {
            normal: {
              width: 3,
            },
          },
          areaStyle: {
            normal: {
              color: "#fe2020aa",
              shadowColor: "rgba(0, 0, 0, 0.1)",
              shadowBlur: 10,
            },
          },
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "#FA9C1F" },
                { offset: 1, color: "#18315B" },
              ],
            },
          },
          data: [0],
        },
        {
          name: "已处理",
          type: "bar",
          smooth: true,
          symbol: "circle",
          barMaxWidth: 10,
          symbolSize: 5,
          showSymbol: false,
          lineStyle: {
            normal: {
              width: 3,
            },
          },
          areaStyle: {},
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "#12E7F9" },
                { offset: 1, color: "#18315B" },
              ],
            },
            // emphasis: {
            // 	color: '#48bc0fff'
            // }
          },
          data: [0],
        },
      ],
    };

    const tableData = [
      { content: "设备XXXXXX1，低温告警", time: "2020/04/12 23:22:12" },
      { content: "设备XXXXXX2，低温告警", time: "2020/04/12 23:22:12" },
      { content: "设备XXXXXX3，低温告警", time: "2020/04/12 23:22:12" },
      { content: "设备XXXXXX4，低温告警", time: "2020/04/12 23:22:12" },
      { content: "设备XXXXXX6，低电量告警", time: "2020/04/12 23:22:12" },
      { content: "设备XXXXXX7，高温报警", time: "2020/04/12 23:22:12" },
      { content: "设备XXXXXX7，高温报警", time: "2020/04/12 23:22:12" },
    ];
    const tableColumns = [
      {
        title: "告警内容",
        key: "content",
      },
      {
        title: "时间",
        key: "time",
      },
    ];

    const displayObj = {
      deviceTotalNum: 0,
      deviceOnlineNum: 0,
      deviceOfflineNum: 0,

      alarmTotalNum: 0,
      alarmHandleNum: 0,
      alarmUnhandleNum: 0,

      assetTotalNum: 0,
      assetLastDayNum: 0,
      assetLastMonthNum: 0,

      accountTotalNum: 0,
      accountLastDayNum: 0,
      accountLastMonthNum: 0,
    };

    let alarmDatas = [];

    const imgUrl = {
      payUrl: "",
      appUrl: "",
      deviceUrl: "",
      projectUrl: "",
    };
    return {
      theme: "dark",
      titleColor: "#17233d",
      currentDeviceTypeIndex: -1,
      deviceTypeChartOption,
      deviceChartOption,
      deviceOnlineChartOption,
      alarmTypeChartOption,
      alarmHandleChartOption,

      tableData,
      tableColumns,

      displayObj,
      alarmDatas,

      imgUrl,
      roleId: undefined,
      alarmRankList: [],
      trendType: 1,

      project: {},
      sum: 0,
      onNum: 0,

      latelyList: [], //最近访问
      newDynamicList: [], // 最新动态
      
      // 添加菜单数据加载状态
      menuDataReady: false,
    };
  },
  activated() {
    if (this.$route.query.reload) {
      this.$router.push("/manage/overview");
      location.reload();
    }
    // 最近访问
    this.getLatelyList();
  },
  created() {
    // 工地详情
    Util.request("/index/project").then((resp) => {
      let respData = resp.data;
      if (respData.success) {
        this.project = respData.data;
        console.log(this.project.weatherData);
      }
    });

    // Util.request("/index/user").then((resp) => {
    //   let respData = resp.data;
    //   if (respData.success) {
    //     this.sum = respData.data.sum;
    //     this.onNum = respData.data.onNum;
    //   }
    // });
    // 最新动态
    Util.request(
      "/appMessageCenter/getPage",
      {
        page: { current: 1, size: 7 },
        customQueryParams: { title: null, createTime: null, content: null, type: null },
        sorts: [],
      },
      "post"
    ).then((resp) => {
      let respData = resp.data;
      if (respData.success) {
        console.log("最新动态", respData);
        this.newDynamicList = respData.data.records;
      }
    });
    // 最近访问
    this.getLatelyList();
  },
  mounted() {
    // 检查菜单数据是否已加载完成
    this.waitForMenuData(() => {
      console.log('菜单数据已加载完成，页面可以正常渲染');
    });
    
    // this.initIcon()
    // this.initDeviceTypeChart();
    this.changeEChartColor(this.$Util.local.get("themeColor"));
    this.$bus.on("change-theme", (theme) => {
      this.changeEChartColor(theme);
      console.log(theme);
      this.theme = theme;
      if (theme === "dark") {
        // "深色";
        this.titleColor = "#fff";
      } else if (theme === "light") {
        // "浅色";
        this.titleColor = "#17233d";
      }
    });
  },
  unmounted() {
    if (this.timmer) {
      clearInterval(this.timmer);
      this.timmer = null;
    }
  },
  methods: {
    // 检查菜单数据是否已加载完成
    checkMenuDataReady() {
      const windowToken = this.$Util.getWindowToken();
      const menusData = this.$Util.local.get(windowToken + "menus");
      if (menusData) {
        try {
          JSON.parse(menusData);
          this.menuDataReady = true;
          return true;
        } catch (e) {
          console.error('菜单数据解析失败:', e);
          return false;
        }
      }
      return false;
    },
    
    // 等待菜单数据加载完成
    waitForMenuData(callback) {
      if (this.checkMenuDataReady()) {
        callback();
      } else {
        // 每100ms检查一次，最多等待10秒
        let checkCount = 0;
        const maxChecks = 100; // 10秒
        
        const checkInterval = setInterval(() => {
          checkCount++;
          if (this.checkMenuDataReady()) {
            clearInterval(checkInterval);
            callback();
          } else if (checkCount >= maxChecks) {
            clearInterval(checkInterval);
            console.warn('菜单数据加载超时');
            // 超时后也执行回调，避免页面一直白屏
            callback();
          }
        }, 100);
      }
    },
    
    toFirstChildLink(authCode){
      this.waitForMenuData(() => {
        let url = this.getRouterLinkUrl(authCode)
        if(url){
          this.toLink("/"+url)
        }
      });
    },
    checkAuthByPerantAuthCode(authCode){
      if (!this.menuDataReady) {
        return false;
      }
      let url = this.getRouterLinkUrl(authCode)
      if(url){
        return true
      }else{
        return false
      }
    },
    getRouterLinkUrl(authCode){
      // 确保菜单数据已加载
      if (!this.menuDataReady) {
        return null;
      }
      
      // console.log('authCode===='+authCode);
      if('videoMonitor' == authCode){
        if(this.$Util.checkAuth('video_surveillance')||this.$Util.checkAuth('image_progress_capture')){
          return 'manage/videoMonitor'
        }
      }
      // console.log('￥router===='+this.$router);
      // console.log('menus===='+this.$Util.local.get("menus"));
      let menus = JSON.parse(this.$Util.local.get(this.$Util.getWindowToken()+"menus"));
      for(let i=0; i< menus.length ; i++){
        let menu = menus[i];
        if(menu.code == authCode){
          let children = menu.childer;
          if(!_.isNil(children) && children.length > 0){
            for(let j=0; j< children.length ; j++){
              let c = children[j];
              // console.log('c.checkAuth===='+this.$Util.checkAuth(c.code));
              if(this.$Util.checkAuth(c.code)){
                // console.log('c===='+JSON.stringify(c));
                // console.log('c.url===='+c.url);
                return c.url
              }
            }
          }
        }
      }
    },

    getLatelyList() {
      // 获取最近访问
      Util.request(
        "/operatelog/visitPrivilegeLog/getPage",
        {
          customQueryParams: { name: "" },
          page: {
            current: 0,
            size: 3,
          },
        },
        "post"
      ).then((resp) => {
        let respData = resp.data;
        if (respData.success) {
          // console.log(respData.data.records, "最近访问");
          this.latelyList = respData.data.records;
        }
      });
    },
    changeTrendType(val) {},
    getTime(time) {
      if (_.isNil(time)) {
        return "";
      }
      return this.$moment(time).format("YYYY-MM-DD");
    },
    toLink(url) {
      this.$router.push({
        path: url,
      });
    },

    changeEChartColor(theme) {
      this.theme = theme;
      if (theme === "dark") {
        this.deviceChartOption.series[0].itemStyle.color.colorStops = [
          { offset: 0, color: "#2790DC" },
          { offset: 1, color: "#12E7F9" },
        ];
        this.deviceOnlineChartOption.series[0].areaStyle.opacity = 1;
        this.deviceOnlineChartOption.series[0].itemStyle.emphasis.shadowBlur = 10;
        this.deviceOnlineChartOption.series[1].areaStyle.opacity = 1;
        this.deviceOnlineChartOption.series[1].itemStyle.emphasis.shadowBlur = 10;

        this.alarmHandleChartOption.series[0].itemStyle.color.colorStops = [
          { offset: 0, color: "#FA9C1F" },
          { offset: 1, color: "#18315B" },
        ];
        this.alarmHandleChartOption.series[1].itemStyle.color.colorStops = [
          { offset: 0, color: "#12E7F9" },
          { offset: 1, color: "#18315B" },
        ];
      } else {
        this.deviceChartOption.series[0].itemStyle.color.colorStops = [
          { offset: 0, color: "#0F71F0" },
          { offset: 1, color: "#009FFF" },
        ];
        this.deviceOnlineChartOption.series[0].areaStyle.opacity = 0;
        this.deviceOnlineChartOption.series[0].itemStyle.emphasis.shadowBlur = 5;
        this.deviceOnlineChartOption.series[1].areaStyle.opacity = 0;
        this.deviceOnlineChartOption.series[1].itemStyle.emphasis.shadowBlur = 5;

        this.alarmHandleChartOption.series[0].itemStyle.color.colorStops = [
          { offset: 0, color: "#00E9E2" },
          { offset: 1, color: "#00D1C3" },
        ];
        this.alarmHandleChartOption.series[1].itemStyle.color.colorStops = [
          { offset: 0, color: "#009FFF" },
          { offset: 1, color: "#0070FB" },
        ];
      }
    },
    deviceTypeChartOut() {
      this.initDeviceTypeChart();
    },
    deviceTypeChartSeover() {
      //清空选中
      this.$refs["deviceTypeChart"].dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        dataIndex: this.currentDeviceTypeIndex,
      });
      if (this.timmer) {
        clearInterval(this.timmer);
        this.timmer = null;
      }
    },
    initDeviceTypeChart() {
      if (!this.timmer) {
        let that = this;
        this.timmer = setInterval(() => {
          if (this.timmer) {
            let dataLen = that.deviceTypeChartOption.series[0].data.length;
            // 取消之前高亮的图形
            that.$refs["deviceTypeChart"].dispatchAction({
              type: "downplay",
              seriesIndex: 0,
              dataIndex: that.currentDeviceTypeIndex,
            });
            that.currentDeviceTypeIndex = (that.currentDeviceTypeIndex + 1) % dataLen;
            // 高亮当前图形
            that.$refs["deviceTypeChart"].dispatchAction({
              type: "highlight",
              seriesIndex: 0,
              dataIndex: that.currentDeviceTypeIndex,
            });
          }
        }, 4000);
      }
    },
    // 根据主题换icon
    initIcon() {
      if (Util.theme == "themeDark") {
        this.imgUrl.payUrl = require("@/images/icon1_2.png");
        this.imgUrl.appUrl = require("@/images/icon2_2.png");
        this.imgUrl.deviceUrl = require("@/images/icon3_2.png");
        this.imgUrl.projectUrl = require("@/images/icon4_2.png");
      } else {
        this.imgUrl.payUrl = require("@/images/icon1_1.png");
        this.imgUrl.appUrl = require("@/images/icon2_1.png");
        this.imgUrl.deviceUrl = require("@/images/icon3_1.png");
        this.imgUrl.projectUrl = require("@/images/icon4_1.png");
      }
    },
  },
};
</script>
