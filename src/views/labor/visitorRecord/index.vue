<template>
	<div>
		<Card dis-hover class="search-card">
			<Form :model="formObj" :label-width="70" ref="formObj">
				<Row>
					<Col span="6">
					<FormItem label="姓名" prop="name">
						<Input v-model.trim="formObj.name" placeholder="请输入" clearable></Input>
					</FormItem>
					</Col>
					<Col span="6">
					<FormItem label="状态" prop="status">
						<Select v-model="formObj.status" clearable filterable>
							<Option :value="1">已出场</Option>
							<Option :value="0">未出场</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="6">
					<FormItem label="时间" prop="timeRange">
						<DatePicker type="daterange" v-model="formObj.timeRange" placement="bottom-end"
							placeholder="选择时间" format="yyyy-MM-dd"></DatePicker>
					</FormItem>
					</Col>
					<Col span="5.5" style="margin-left: 10px;">
					<Button @click="resetSearch">重置</Button>
					<Button type="primary" @click="getData">搜索</Button>
					</Col>
				</Row>
			</Form>
		</Card>
		<ContentCard>
			<div class="btn-content">
				<Button type="primary" @click="handleOpen">访客二维码</Button>
			</div>
			<EmpTable :columns="columns" url="/lobar/visitorRecord/listPage" ref="deviceTb" :page-size="7"
				:page-opts="[7, 14, 20, 30, 50, 100]" />
		</ContentCard>
		<!-- 访客二维码预览 -->
		<Modal v-model="modal1" width="300">
			<p slot="header" style="text-align:center">
				<span>访客二维码</span>
			</p>
			<div class="rq-img" id="qrcode" ref="qrCodeUrl"></div>
			<div slot="footer">
				<Button @click="handleDnowload">下载</Button>
			</div>
		</Modal>
	</div>
</template>
<script>
import Util from "@/libs/util";
import { columns } from "./columns";
import QRCode from "qrcodejs2";
export default {
	name: "visitRecords",
	data () {
		return {
			formObj: {
				timeRange: [],
				name: "",
				status: ""
			},
			columns: columns(this),
			statusList: ["未出场", "已出场"],
			codeStatus: ["正常", "异常"],
			modal1: false
		};
	},
	mounted () {
		this.getData();
	},
	methods: {
		handleOpen () {
			const windowToken = Util.getWindowToken()
			const user = JSON.parse(Util.local.get(windowToken + 'user'))
			this.modal1 = true
			this.initRqCode(user.tenantId);
		},
		initRqCode (tenantId) {
			console.log(tenantId)
			document.getElementById("qrcode").innerHTML = "";
			let qrcode = new QRCode(this.$refs.qrCodeUrl, {
				text: `${window.location.origin}/constructionapph5/#/visitorRecord?tenantId=${tenantId}`, // 需要转换为二维码的内容
				width: 200,
				height: 200,
				colorDark: "#000000",
				colorLight: "#ffffff",
				correctLevel: QRCode.CorrectLevel.L
			});
		},
		getData () {
			const param = Util.objClone(this.formObj);
			param.startTime = param.timeRange[0]
				? Util.formatDate(param.timeRange[0], "yyyy-MM-dd") +
				" 00:00:00"
				: "";
			param.endTime = param.timeRange[1]
				? Util.formatDate(param.timeRange[1], "yyyy-MM-dd") +
				" 23:59:59"
				: "";
			this.$refs.deviceTb.search(param);
		},
		resetSearch () {
			this.$refs["formObj"].resetFields();
			this.getData();
		},
		handleOperation (data) {
			this.$Modal.confirm({
				title: "出场确认",
				content: `确认${data.name}出场？`,
				loading: true,
				onOk: () => {
					Util.request(
						`/lobar/visitorRecord/visitorOut/${data.id}`,
						{},
						"post"
					).then(res => {
						this.$Modal.remove();
						if (res.data.success) {
							this.$Message.success("出场成功");
							this.getData();
						} else {
							this.$Message.success("出场失败");
						}
					});
				}
			});
		},
		handleRender () {
			this.$Modal.confirm({
				render: h => {
					return h("div", {
						attrs: {
							id: "qrcode",
							ref: "qrCodeUrl"
						}
					});
				}
			});
			this.$nextTick(() => { });
		},
		handleDnowload() {
			const elImage = document.getElementById("qrcode").children[1]
			if (elImage) {
				const content = elImage.getAttribute('src')
				console.log(content)
				let eleLink = document.createElement("a");
				eleLink.href = content; // 转换后的图片地址
				eleLink.download = '访客二维码';
				document.body.appendChild(eleLink);
				eleLink.click();
				document.body.removeChild(eleLink);


			}
			this.modal1 = false

		}
	}
};
</script>
<style lang="less" scoped>
.rq-img {
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
