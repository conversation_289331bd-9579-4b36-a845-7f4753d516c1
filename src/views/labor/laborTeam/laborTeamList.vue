<template>
  <ContentCard class="laborTeamList">
    <div class="page-container">
      <div class="main-content">
        <!-- 查询表单 -->
		<BaseForm
			:model="searchObj"
			:label-width="100"
			:init-form-data="true"
			@handleSubmit="getData"
			@handleReset="resetSearch"
		>
			<template #formitem>
				<FormItem label="参建单位名称">
                  <Input v-model.trim="searchObj.laborTeamName" placeholder="请输入" clearable />
                </FormItem>
				<FormItem label="负责人姓名">
                  <Input v-model.trim="searchObj.leaderName" placeholder="请输入" clearable />
                </FormItem>
			</template>
		</BaseForm>
        <!-- 按钮区 -->
        <btnCard>
          <Button type="primary" v-auth="'t_labor_team_add'" @click="openModal()">新增</Button>
        </btnCard>
        <!-- 表格 -->
        <EmpTable
          ref="laborTeamTb"
          :columns="columns"
          url="/laborTeam/getPage"
          :query-params="getQueryParams"
          :pageSize="10"
          :pageOpts="[10,20,50,100]"
          :loadDone="tableLoadDone"
        >
          <template slot-scope="{ row }" slot="contractFiles">
            <div v-if="row.contractFiles && row.contractFiles.length">
              <div
                v-for="(file, idx) in row.contractFiles"
                :key="file.url || file"
                style="margin-bottom: 4px;"
              >
                <a
                  :href="file.url || file"
                  target="_blank"
                  :title="file.name || (typeof file === 'string' ? file.split('/').pop() : '')"
                >
                  {{ (file.name || (typeof file === 'string' ? file.split('/').pop() : '')).length > 10
                     ? (file.name || (typeof file === 'string' ? file.split('/').pop() : '')).substring(0, 10) + '...'
                     : (file.name || (typeof file === 'string' ? file.split('/').pop() : '')) }}
                </a>
              </div>
            </div>
          </template>
          <template slot-scope="{ row }" slot="action">
            <LinkBtn v-auth="'t_labor_team_edit'" @click="openModal(row)">编辑</LinkBtn>
            <LinkBtn v-auth="'t_labor_team_delete'" @click="handleDelete(row)">删除</LinkBtn>
          </template>
        </EmpTable>
        <!-- 新增/编辑弹框 -->
        <Modal v-model="showModal" :title="modalTitle" :mask-closable="false" :closable="false" width="600">
          <Form ref="formRef" :model="form" :rules="rules" :label-width="120">
            <FormItem label="参建单位名称" prop="laborTeamName">
              <Input v-model.trim="form.laborTeamName" maxlength="50" placeholder="请输入" />
            </FormItem>
            <FormItem label="统一社会信用代码" prop="creditCode">
              <Input v-model.trim="form.creditCode" maxlength="18" placeholder="请输入" />
            </FormItem>
            <FormItem label="负责人姓名" prop="leaderName">
              <Input v-model.trim="form.leaderName" maxlength="10" placeholder="请输入" />
            </FormItem>
            <FormItem label="负责人手机号" prop="leaderPhone">
              <Input v-model.trim="form.leaderPhone" maxlength="20" placeholder="请输入" />
            </FormItem>
            <FormItem label="备注" prop="remark">
              <Input v-model.trim="form.remark" type="textarea" maxlength="100" placeholder="请输入" show-word-limit />
            </FormItem>
            <FormItem label="合同附件" prop="contractFiles">
              <FilesUpload
                :value="''"
                :format="['pdf']"
                :max-size="1024*1024*1024"
                :showonly="false"
                :showImageFlag="false"
                @input="handleFileUpload"
              />
              <div v-for="(item, idx) in form.contractFiles" :key="item.url" style="margin-top: 8px;">
                <a :href="item.url" target="_blank">{{ item.name }}</a>
                <Button type="text" @click="removeFile(idx)" size="small">删除</Button>
              </div>
            </FormItem>
          </Form>
          <div slot="footer">
            <Button @click="showModal=false">取消</Button>
            <Button type="primary" @click="handleSubmit">确认</Button>
          </div>
        </Modal>
      </div>
    </div>
  </ContentCard>
</template>

<script>
import EmpTable from '@/components/EmpTable.vue'
import Util from '@/libs/util'
import FilesUpload from '@/components/FilesUpload.vue'

export default {
  name: 'LaborTeamList',
  components: { EmpTable, FilesUpload },
  data() {
    return {
      searchObj: {
        laborTeamName: '',
        leaderName: ''
      },
      columns: [
        { type: 'selection', width: 50, align: 'center' },
        { title: '序号', type: 'index', width: 60, align: 'center' },
        { title: '统一社会信用代码', key: 'creditCode', minWidth: 150, tooltip: true },
        { title: '参建单位名称', key: 'laborTeamName', minWidth: 180, tooltip: true },
        { title: '负责人', key: 'leaderName', minWidth: 100 },
        { title: '负责人手机号', key: 'leaderPhone', minWidth: 130 },
        { title: '人数', key: 'personCount', minWidth: 80 },
        { title: '合同附件', key: 'contractFiles', slot: 'contractFiles', minWidth: 120 },
        { title: '创建时间', key: 'createTime', minWidth: 150 },
        { title: '操作', slot: 'action', width: 120, align: 'center' }
      ],
      showModal: false,
      modalTitle: '新增参建单位',
      form: {
        id: null,
        laborTeamName: '',
        creditCode: '',
        leaderName: '',
        leaderPhone: '',
        remark: '',
        contractFiles: []
      },
      fileList: [],
      rules: {
        laborTeamName: [
          { required: true, message: '请输入参建单位名称', trigger: 'blur' },
          { max: 50, message: '最多50字', trigger: 'blur' }
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
          { max: 18, message: '最多18位', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
                return;
              }
              // 统一社会信用代码格式校验
              const reg = /^[0-9A-Z]{18}$/;
              if (!reg.test(value)) {
                callback(new Error('统一社会信用代码格式不正确'));
                return;
              }
              // 校验位算法
              const code = value.toUpperCase();
              const baseCode = '0123456789ABCDEFGHJKLMNPQRTUWXY';
              const weights = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28];
              let sum = 0;
              for (let i = 0; i < 17; i++) {
                const codeIndex = baseCode.indexOf(code[i]);
                if (codeIndex === -1) {
                  callback(new Error('统一社会信用代码格式不正确'));
                  return;
                }
                sum += codeIndex * weights[i];
              }
              const logicCheckCode = (31 - (sum % 31)) % 31;
              const checkChar = baseCode[logicCheckCode];
              if (checkChar !== code[17]) {
                callback(new Error('统一社会信用代码校验位不正确'));
                return;
              }
              callback();
            },
            trigger: 'blur'
          }
        ],
        leaderName: [
          { max: 10, message: '最多10字', trigger: 'blur' }
        ],
        leaderPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ],
        remark: [
          { max: 100, message: '最多100字', trigger: 'blur' }
        ]
      },
      uploadUrl: '/api/upload', // TODO: 替换为实际上传接口
      uploadHeaders: {},
      uploadData: {},
      deleteLoading: false
    }
  },
  created() {
  },
  mounted() {
    // 页面初始化时执行查询
    this.getData()
  },
  methods: {
    getQueryParams(params) {
      // EmpTable自定义查询参数
      return {
        ...params,
        customQueryParams: {
          ...this.searchObj
        }
      }
    },
    tableLoadDone(data) {
      // 可根据需要处理表格加载完成后的逻辑
    },
    resetSearch() {
      this.searchObj = { laborTeamName: '', leaderName: '' }
      this.getData()
    },
    getData() {
      this.$refs.laborTeamTb.search(this.searchObj)
    },
    openModal(row) {
      if (row) {
        this.modalTitle = '编辑参建单位'
        this.form = { ...row, contractFiles: (row.contractFiles || []).map(f => {
          if (typeof f === 'string') {
            return { url: f, name: f.split('/').pop() };
          } else {
            return f;
          }
        }) }
        this.fileList = this.form.contractFiles.map((f, idx) => ({
          name: f.name,
          url: f.url,
          status: 'finished'
        }))
      } else {
        this.modalTitle = '新增参建单位'
        this.form = {
          id: null,
          laborTeamName: '',
          creditCode: '',
          leaderName: '',
          leaderPhone: '',
          remark: '',
          contractFiles: []
        }
        this.fileList = []
      }
      this.showModal = true
    },
    handleFileUpload(url) {
      if (url && !this.form.contractFiles.find(f => f.url === url)) {
        if (this.form.contractFiles.length >= 10) {
          this.$Message.warning('最多上传10个文件');
          return;
        }
        // 解析真实文件名
        const name = url.split('/').pop();
        this.form.contractFiles.push({ url, name });
      }
    },
    removeFile(idx) {
      this.form.contractFiles.splice(idx, 1);
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.form.id ? '/laborTeam/edit' : '/laborTeam/save'
        const data = {
          ...this.form,
          contractFiles: this.form.contractFiles.map(f => f.url)
        }
        Util.request(api, data, 'post').then(res => {
          if (res.data.code === 'success') {
            this.$Message.success({ background: true, content: this.form.id ? '编辑成功' : '新增成功' });
            this.showModal = false
            this.getData()
          } else {
            this.$Message.error({ background: true, content: res.data.message || '操作失败' })
          }
        })
      })
    },
    handleDelete(row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认要删除该参建单位吗？</p>',
        onOk: () => {
          Util.request(`/laborTeam/del?ids=${row.id}`, {}, 'delete').then(res => {
            if (res.data.code === 'success') {
              this.$Message.success({ background: true, content: '删除成功' })
              this.getData()
            } else {
              this.$Message.error(res.data.message || '删除失败')
            }
          })
        }
      })
    },
    handleBatchDelete() {
      const selection = this.$refs.laborTeamTb.getSelection()
      if (!selection.length) {
        this.$Message.warning('请先选择要删除的数据')
        return
      }
      const ids = selection.map(item => item.id).join(',')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认要删除选中的参建单位吗？</p>',
        onOk: () => {
          Util.request(`/laborTeam/del?ids=${ids}`, {}, 'get').then(res => {
            if (res.data.code === 'success') {
              this.$Message.success({ background: true, content: '删除成功' })
              this.$refs.laborTeamTb.refresh()
            } else if (res.data.code === 400 && res.data.message.includes('需先解除员工与队伍的关联关系')) {
              this.$Message.warning('需先解除员工与队伍的关联关系')
            } else {
              this.$Message.error(res.data.message || '删除失败')
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.laborTeamList {
  min-width: 1050px;
  .page-container {
    display: flex;
    height: 100%;
    .main-content {
      flex: 1;
      min-width: 0;
    }
  }
  .btn-content {
    margin-bottom: 16px;
    button + button {
      margin-left: 8px;
    }
  }
}
</style>
