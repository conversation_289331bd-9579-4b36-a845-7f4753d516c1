<template>
  <!-- <router-view></router-view> -->
  <div v-if="respData">
    <div v-if="editorType == '3d'">
      <SceneEditor v-if="type == 'edit'" :data="respData" />
      <SceneEditorShow v-else :data="respData"/>
    </div>
    <div v-if="editorType == '2d'">
      <SceneEditor2d v-if="type == 'edit'" :data="respData" />
      <SceneEditorShow2d v-else :data="respData"/>
    </div>
  </div>
</template>

<script>
import SceneEditor from '@/views/ht-editor/scene-editor.vue'
import SceneEditorShow from '@/views/ht-editor/scene-editor-show.vue'
import SceneEditor2d from '@/views/ht-editor/scene-editor2d.vue'
import SceneEditorShow2d from '@/views/ht-editor/scene-editor2d-show.vue'
import {EditorCfg} from "./config";

export default {
  name: 'HtIndex',
  components: {
    SceneEditor,
    SceneEditorShow,
    SceneEditor2d,
    SceneEditorShow2d
  },
  data() {
    return {
      type: '',
      respData: null,
      editorType: ''
    }
  },
  created() {
    Util.local.delete$saasToken$()
    this.token = EditorCfg.getURLParameter('token');
      if (!this.token) {
      this.$Message.error('url错误，无效的场景代码')
      return;
    }
    this.type = EditorCfg.getURLParameter('type');
    // 实际访问时先退出
    if (this.type != 'edit' && this.type != 'preview') {
      Util.request('/logout', {}, 'post').then(resp => {
        Util.local.clearSession()
        // 加载数据
        this.loadView('openapi').then(respData => {
          // 已发布时自动登录
          if (respData.instance.publishStatus) {
            this.loginByToken(respData.saasTenantId)
            .then(resp => {
              this.editorType = respData.template.type
              this.respData = respData;
            })
          } else {
           this.$Modal.error({
              content: '无法访问可视化，请先发布！'
            }) 
          }
        })
      })
    } else {
      const ssoUrl = EditorCfg.getURLParameter('ssoUrl')
      if (ssoUrl) {
        window.ssoUrl = decodeURIComponent(ssoUrl)
        Util.request('/logout', {}, 'post').then(resp => {
          // 退出要清除缓存
          Util.local.clearSession()
          Util.local.set('redirect_uri', Util.removeURLParameter('ssoUrl'))
          location.href = window.ssoUrl
        })
      } else {
        this.loadView('openAPI').then((respData) => {
          if (this.type === 'edit') {
            window.onbeforeunload = function(event) {
              event.returnValue = '请确保所有改动已保存，否则关闭将丢失改动，确定要退出编辑器？'
              return event.returnValue
            }
          }
          this.editorType = respData.template.type
          this.respData = respData
        })
      }
    }
  },
  methods: {
    loadView(url) {
      return Util.requestPost(url + '/get3dData?token='+ this.token).then(resp => {
        let respData = resp.data.data
        if (respData && respData.instance && respData.template) {
          return respData
        } else {
          this.$Modal.error({
            content: '没有找到图纸'
          })
          return Promise.reject('没有找到图纸')
        }
      })
    },
    loginByToken(token) {
      return Util.requestGet('/login/info', { Pragma: 'no-cache', 'Cache-Control': 'no-cache', token: token }).then(resp => {
        let respData = resp.data
        if (!(respData.success && respData.data.user)) {
          Util.local.clearSession()
        } else {
          Util.local.set$saasToken$(token)
          Util.local.set('isLogin', 1)
          const data = respData.data;
          const authData = respData.data.auth;
          let auth = {};
          authData.forEach(item => {
            auth[item.code] = true;
          })
          Util.local.set(Util.getWindowToken() + 'auth', JSON.stringify(auth || '{}'))
          // Util.local.set('auth', JSON.stringify(auth || '{}'))
          Util.local.set(Util.getWindowToken() + 'user', JSON.stringify(data.user))
        }
      })
    }
  }
}
</script>

<style lang="" scoped>
  
</style>