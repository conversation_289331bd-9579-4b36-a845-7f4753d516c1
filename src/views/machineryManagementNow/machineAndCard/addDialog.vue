<template>
  <Modal v-model="isModalShow" :title="title" width="1300" :mask-closable="false">
    <Form ref="form" :model="formData" :rules="rules" :label-width="120" class="form-container">
      <Row :gutter="16">
        <Col span="8">
          <FormItem label="项目名称" prop="projectName">
            <Input v-model="formData.projectName" disabled />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="机械使用工点" prop="workPointId">
            <SelectByUrl
              label-field="pointName"
              value-field="id"
              v-model="formData.workPointId"
              url="/project/projectPoint/listPage"
              placeholder="请选择"
              clearable
            />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="机械类型" prop="type">
            <DictSelect v-model="formData.type" dict-name="机械类型" placeholder="请选择机械类型" @on-change="handleChange" />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="机械编号" prop="code">
            <Input v-model="formData.code" placeholder="请输入机械编号" />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="机械所属" prop="ownerShipType">
            <Select v-model="formData.ownerShipType" placeholder="请选择机械所属" @on-change="handleOwnershipChange">
              <Option value="1">自有</Option>
              <Option value="2">租赁</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="机械所属公司" prop="ownerShipName">
            <Input v-model="formData.ownerShipName" :disabled="formData.ownerShipType === '1'" placeholder="请输入机械所属公司" />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="机械名称" prop="name">
            <Input v-model="formData.name" placeholder="请输入机械名称" disabled />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="规格型号" prop="specification">
            <Input v-model="formData.specification" placeholder="请输入规格型号" maxlength="20" show-word-limit />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="进场时间" prop="enterTime" class="">
            <DatePicker 
              type="datetime" 
              v-model="formData.enterTime" 
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择进场时间"
              style="width: 100%"
            />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="驾乘人员" prop="driverIds">
            <Select v-model="formData.driverIds" filterable clearable placeholder="请选择驾乘人员">
              <Option v-for="item in drivers" :key="item.id" :value="item.id.toString()">{{ item.realName }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="施工单位管理人员" prop="managerId">
            <Select v-model="formData.managerId" filterable clearable placeholder="请选择管理人员">
              <Option v-for="item in managers" :key="item.id" :value="item.id.toString()">{{ item.realName }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="是否特种机械" prop="isSpecialEquipment">
            <RadioGroup v-model="formData.isSpecialEquipment">
              <Radio label="1">是</Radio>
              <Radio label="0">否</Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <!-- <Col span="8">
          <FormItem label="现场安装机械验收情况" prop="machineryAcceptanceStatus">
            <Input v-model="formData.machineryAcceptanceStatus" placeholder="请输入现场安装机械验收情况" />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="现场安装机械计划伐拆时间" prop="plannedRemovalTime">
            <DatePicker type="date" v-model="formData.plannedRemovalTime" />
          </FormItem>
        </Col> -->
        <Col span="8">
          <FormItem label="有无动力" prop="isPower">
            <RadioGroup v-model="formData.isPower">
              <Radio label="1">有</Radio>
              <Radio label="0">无</Radio>
            </RadioGroup>
          </FormItem>
        </Col>
      </Row>
      <Divider>厂家信息</Divider>
      <Row :gutter="16">
        <Col span="12">
          <FormItem label="生产厂家" prop="manufacturer">
            <Input v-model="formData.manufacturer" placeholder="请输入生产厂家" />
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem label="厂家联系方式" prop="manufacturerTel">
            <Input v-model="formData.manufacturerTel" placeholder="请输入厂家联系方式" />
          </FormItem>
        </Col>
      </Row>
      <Divider>机械维护信息</Divider>
      <Row :gutter="16">
        <Col span="8">
          <FormItem label="维护人" prop="maintainer">
            <Input v-model="formData.maintainer" placeholder="请输入维护人" />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="维护人联系方式" prop="maintainerTel">
            <Input v-model="formData.maintainerTel" placeholder="请输入维护人联系方式" />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="维护日期" prop="maintainerDate">
            <DatePicker type="date" v-model="formData.maintainerDate" style="width: 100%;" />
          </FormItem>
        </Col>
      </Row>
      <Divider>机械资料</Divider>

      <Row :gutter="16">
        <Col span="12">
          <FormItem label="机械进场报验表">
            <UploadFile 
              v-model="formData.inspectionFileList" 
              :max-count="1"
              :multiple="false"
              :format-arr="['pdf', 'jpg', 'jpeg', 'png']"
              :max-size="10240"
              :upload-type="'select'"
            />
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem label="合格证">
            <UploadFile 
              v-model="formData.certificateFileList" 
              :max-count="1"
              :multiple="false"
              :upload-type="'select'"
              :format-arr="['pdf', 'jpg', 'jpeg', 'png']"
              :max-size="10240"
            />
          </FormItem>
        </Col>
      </Row>

      <Row :gutter="16">
        <Col span="12">
          <FormItem label="鉴定证书">
            <UploadFile 
              :multiple="false"
              v-model="formData.appraisalCertificateFileList" 
              :max-count="1"
              :upload-type="'select'"
              :format-arr="['pdf', 'jpg', 'jpeg', 'png']"
              :max-size="10240"
            />
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem label="设备登记使用证">
            <UploadFile 
              v-model="formData.registrationCertificateFileList" 
              :max-count="1"
              :multiple="false"
              :upload-type="'select'"
              :format-arr="['pdf', 'jpg', 'jpeg', 'png']"
              :max-size="10240"
            />
          </FormItem>
        </Col>
      </Row>

      <Row :gutter="16">
        <Col span="24">
          <FormItem label="机械照片">
            <UploadImg 
              :multiple="false"
              v-model="formData.equipmentPhotoFileList" 
              :max-number="5"
              :format-arr="['jpg', 'jpeg', 'png']"
              :max-size="10240"
            />
          </FormItem>
        </Col>
      </Row>

      <!-- 动态显示机械类型相关字段 -->
      <template v-if="formData.type">
        <!--机械信息-->
        <Divider>机械信息</Divider>
        <!-- 装载机 -->
        <template v-if="formData.type === '1'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="额定载重量" prop="edzl">
                <Input v-model="specificalInfo.edzl" placeholder="请输入额定载重量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="铲斗容量" prop="cdrl">
                <Input v-model="specificalInfo.cdrl" placeholder="请输入铲斗容量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="卸载高度" prop="xzgd">
                <Input v-model="specificalInfo.xzgd" placeholder="请输入卸载高度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="卸载距离" prop="xzjl">
                <Input v-model="specificalInfo.xzjl" placeholder="请输入卸载距离" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="发动机功率" prop="fdjgl">
                <Input v-model="specificalInfo.fdjgl" placeholder="请输入发动机功率" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="整机重量" prop="zjzl">
                <Input v-model="specificalInfo.zjzl" placeholder="请输入整机重量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="最大牵引力" prop="zdyql">
                <Input v-model="specificalInfo.zdyql" placeholder="请输入最大牵引力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="转向角度" prop="zxjd">
                <Input v-model="specificalInfo.zxjd" placeholder="请输入转向角度" />
              </FormItem>
            </Col>
          </Row>
        </template>

        <!-- 压路机 -->
        <template v-if="formData.type === '2'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="工作质量" prop="gzzl">
                <Input v-model="specificalInfo.gzzl" placeholder="请输入工作质量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="振动频率/振幅" prop="zdplzf">
                <Input v-model="specificalInfo.zdplzf" placeholder="请输入振动频率/振幅" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="碾压宽度" prop="nykd">
                <Input v-model="specificalInfo.nykd" placeholder="请输入碾压宽度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="行走速度" prop="xzsd">
                <Input v-model="specificalInfo.xzsd" placeholder="请输入行走速度" />
              </FormItem>
            </Col>
            
            <Col span="8">
              <FormItem label="发动机功率" prop="fdjgl">
                <Input v-model="specificalInfo.fdjgl" placeholder="请输入发动机功率" />
              </FormItem>
            </Col>
            
            <Col span="8">
              <FormItem label="钢轮直径/宽度" prop="glzjkd">
                <Input v-model="specificalInfo.glzjkd" placeholder="请输入钢轮直径/宽度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="爬坡能力" prop="ppnl">
                <Input v-model="specificalInfo.ppnl" placeholder="请输入爬坡能力" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '3'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="钻孔直径" prop="zkjz">
                <Input v-model="specificalInfo.zkjz" placeholder="请输入钻孔直径" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="最大钻孔深度" prop="zdkjzd">
                <Input v-model="specificalInfo.zdkjzd" placeholder="请输入最大钻孔深度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="动力头扭矩" prop="dltnj">
                <Input v-model="specificalInfo.dltnj" placeholder="请输入动力头扭矩" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="主卷扬提升力" prop="zjtsl">
                <Input v-model="specificalInfo.zjtsl" placeholder="请输入主卷扬提升力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="动力类型（柴油/电动）" prop="dllx">
                <Input v-model="specificalInfo.dllx" placeholder="请输入动力类型" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="桅杆倾斜角度" prop="wdqxj">
                <Input v-model="specificalInfo.wdqxj" placeholder="请输入桅杆倾斜角度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="整机运输尺寸" prop="zjysc">
                <Input v-model="specificalInfo.zjysc" placeholder="请输入整机运输尺寸" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '4'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="整机重量" prop="zjzl">
                <Input v-model="specificalInfo.zjzl" placeholder="请输入整机重量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="铲斗容量" prop="cdrl">
                <Input v-model="specificalInfo.cdrl" placeholder="请输入铲斗容量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="发动机功率" prop="fdjgl">
                <Input v-model="specificalInfo.fdjgl" placeholder="请输入发动机功率" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="最大挖掘深度" prop="zdwjsd">
                <Input v-model="specificalInfo.zdwjsd" placeholder="请输入最大挖掘深度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="最大挖掘高度" prop="zdwjgd">
                <Input v-model="specificalInfo.zdwjgd" placeholder="请输入最大挖掘高度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="最大挖掘半径" prop="zdwjbj">
                <Input v-model="specificalInfo.zdwjbj" placeholder="请输入最大挖掘半径" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="行走速度" prop="xzsd">
                <Input v-model="specificalInfo.xzsd" placeholder="请输入行走速度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="液压系统压力" prop="yyxyl">
                <Input v-model="specificalInfo.yyxyl" placeholder="请输入液压系统压力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="回转速度" prop="hzsd">
                <Input v-model="specificalInfo.hzsd" placeholder="请输入回转速度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="动力类型（电动/燃油）" prop="dllx">
                <Input v-model="specificalInfo.dllx" placeholder="请输入动力类型" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '5'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="最大起重量" prop="zdqzl">
                <Input v-model="specificalInfo.zdqzl" placeholder="请输入最大起重量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="臂长" prop="bc">
                <Input v-model="specificalInfo.bc" placeholder="请输入臂长" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="最大回转半径" prop="zdhzbj">
                <Input v-model="specificalInfo.zdhzbj" placeholder="请输入最大回转半径" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="支腿跨距" prop="ztkj">
                <Input v-model="specificalInfo.ztkj" placeholder="请输入支腿跨距" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="起升高度" prop="qsgd">
                <Input v-model="specificalInfo.qsgd" placeholder="请输入起升高度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="液压系统类型" prop="yyxylx">
                <Input v-model="specificalInfo.yyxylx" placeholder="请输入液压系统类型" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="吊臂结构（伸缩式或折叠式）" prop="dbjg">
                <Input v-model="specificalInfo.dbjg" placeholder="请输入吊臂结构" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="底盘承载能力" prop="dccznl">
                <Input v-model="specificalInfo.dccznl" placeholder="请输入底盘承载能力" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '6'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="跨度" prop="kd">
                <Input v-model="specificalInfo.kd" placeholder="请输入跨度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="起升高度" prop="qsgd">
                <Input v-model="specificalInfo.qsgd" placeholder="请输入起升高度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="额定起重量" prop="edqzl">
                <Input v-model="specificalInfo.edqzl" placeholder="请输入额定起重量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="运行速度" prop="yxsd">
                <Input v-model="specificalInfo.yxsd" placeholder="请输入运行速度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="轨道型号" prop="gdxh">
                <Input v-model="specificalInfo.gdxh" placeholder="请输入轨道型号" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="主梁结构（单梁/双梁）" prop="zljg">
                <Input v-model="specificalInfo.zljg" placeholder="请输入主梁结构" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="操作方式（遥控/驾驶室）" prop="czfs">
                <Input v-model="specificalInfo.czfs" placeholder="请输入操作方式" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '7'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="最大重量" prop="zdzl">
                <Input v-model="specificalInfo.zdzl" placeholder="请输入最大起重量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="大臂长" prop="dbc">
                <Input v-model="specificalInfo.dbc" placeholder="请输入大臂长" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="小臂长" prop="xbc">
                <Input v-model="specificalInfo.xbc" placeholder="请输入小臂长" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="最大起重力矩" prop="zdqzlj">
                <Input v-model="specificalInfo.zdqzlj" placeholder="请输入最大起重力矩" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="塔基高度" prop="tjgd">
                <Input v-model="specificalInfo.tjgd" placeholder="请输入塔基高度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="工作幅度" prop="gzfd">
                <Input v-model="specificalInfo.gzfd" placeholder="请输入工作幅度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="基础承载力" prop="jcczl">
                <Input v-model="specificalInfo.jcczl" placeholder="请输入基础承载力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="塔臂高" prop="tbg">
                <Input v-model="specificalInfo.tbg" placeholder="请输入塔臂高" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="塔帽高" prop="tmg">
                <Input v-model="specificalInfo.tmg" placeholder="请输入塔帽高" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '8'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="最大起重量" prop="zdqzl">
                <Input v-model="specificalInfo.zdqzl" placeholder="请输入最大起重量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="臂长" prop="bc">
                <Input v-model="specificalInfo.bc" placeholder="请输入臂长" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="最大回转半径" prop="zdhzbj">
                <Input v-model="specificalInfo.zdhzbj" placeholder="请输入最大回转半径" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="液压系统类型" prop="yyxylx">
                <Input v-model="specificalInfo.yyxylx" placeholder="请输入液压系统类型" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="吊臂结构（伸缩式或折叠式）" prop="dbjg">
                <Input v-model="specificalInfo.dbjg" placeholder="请输入吊臂结构" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="起升能力" prop="qsnl">
                <Input v-model="specificalInfo.qsnl" placeholder="请输入起升能力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="底盘承载能力" prop="dccznl">
                <Input v-model="specificalInfo.dccznl" placeholder="请输入底盘承载能力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="行驶速度" prop="xssd">
                <Input v-model="specificalInfo.xssd" placeholder="请输入行驶速度" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '9'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="水箱容量" prop="sxrl">
                <Input v-model="specificalInfo.sxrl" placeholder="请输入水箱容量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="喷洒宽度" prop="pskd">
                <Input v-model="specificalInfo.pskd" placeholder="请输入喷洒宽度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="喷头数量" prop="ptsl">
                <Input v-model="specificalInfo.ptsl" placeholder="请输入喷头数量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="水泵流量/压力" prop="sbl">
                <Input v-model="specificalInfo.sbl" placeholder="请输入水泵流量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="底盘载重能力" prop="dczznl">
                <Input v-model="specificalInfo.dczznl" placeholder="请输入底盘载重能力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="水泵压力" prop="sby">
                <Input v-model="specificalInfo.sby" placeholder="请输入水泵压力" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '10'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="臂架长度" prop="bjcd">
                <Input v-model="specificalInfo.bjcd" placeholder="请输入臂架长度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="布料半径" prop="blbj">
                <Input v-model="specificalInfo.blbj" placeholder="请输入布料半径" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="泵送量（m³/h）" prop="bsl">
                <Input v-model="specificalInfo.bsl" placeholder="请输入泵送量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="底盘承载能力" prop="dccznl">
                <Input v-model="specificalInfo.dccznl" placeholder="请输入底盘承载能力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="输送压力" prop="ssyl">
                <Input v-model="specificalInfo.ssyl" placeholder="请输入输送压力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="液压系统压力" prop="yyxyl">
                <Input v-model="specificalInfo.yyxyl" placeholder="请输入液压系统压力" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="支腿跨距" prop="ztkj">
                <Input v-model="specificalInfo.ztkj" placeholder="请输入支腿跨距" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '11'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="额定功率" prop="edgl">
                <Input v-model="specificalInfo.edgl" placeholder="请输入额定功率" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="输出电压/频率" prop="scdy">
                <Input v-model="specificalInfo.scdy" placeholder="请输入输出电压/频率" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="燃料类型（柴油/汽油）" prop="rllx">
                <Input v-model="specificalInfo.rllx" placeholder="请输入燃料类型" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="油箱容量" prop="yxrl">
                <Input v-model="specificalInfo.yxrl" placeholder="请输入油箱容量" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="噪音水平" prop="zysp">
                <Input v-model="specificalInfo.zysp" placeholder="请输入噪音水平" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="连续运行时间" prop="lxyxsj">
                <Input v-model="specificalInfo.lxyxsj" placeholder="请输入连续运行时间" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="整机重量" prop="zjzl">
                <Input v-model="specificalInfo.zjzl" placeholder="请输入整机重量" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '12'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="钻孔直径（金属/混）" prop="zkzj">
                <Input v-model="specificalInfo.zkzj" placeholder="请输入钻孔直径" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="冲击频率" prop="cjpl">
                <Input v-model="specificalInfo.cjpl" placeholder="请输入冲击频率" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="输入功率" prop="srgl">
                <Input v-model="specificalInfo.srgl" placeholder="请输入输入功率" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="转速" prop="zs">
                <Input v-model="specificalInfo.zs" placeholder="请输入转速" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="夹头类型" prop="jtlx">
                <Input v-model="specificalInfo.jtlx" placeholder="请输入夹头类型" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="重量" prop="zl">
                <Input v-model="specificalInfo.zl" placeholder="请输入重量" />
              </FormItem>
            </Col>
          </Row>
        </template>
        <template v-if="formData.type === '13'">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="额定载荷" prop="edzh">
                <Input v-model="specificalInfo.edzh" placeholder="请输入额定载荷" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="起升高度" prop="qsgd">
                <Input v-model="specificalInfo.qsgd" placeholder="请输入起升高度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="门架倾角" prop="mjqj">
                <Input v-model="specificalInfo.mjqj" placeholder="请输入门架倾角" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="转弯半径" prop="zwbj">
                <Input v-model="specificalInfo.zwbj" placeholder="请输入转弯半径" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="动力类型（电动/燃油）" prop="dllx">
                <Input v-model="specificalInfo.dllx" placeholder="请输入动力类型" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="货叉长度" prop="hccd">
                <Input v-model="specificalInfo.hccd" placeholder="请输入货叉长度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="货叉宽度" prop="hckd">
                <Input v-model="specificalInfo.hckd" placeholder="请输入货叉宽度" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="行驶速度" prop="xssd">
                <Input v-model="specificalInfo.xssd" placeholder="请输入行驶速度" />
              </FormItem>
            </Col>
          </Row>
        </template>
      </template>
    </Form>

    <!-- 预览弹窗 -->
    <Modal v-model="previewVisible" footer-hide fullscreen>
      <div class="preview-container">
        <img v-if="isImage" :src="previewUrl" class="preview-image">
        <iframe v-else :src="previewUrl" class="preview-pdf"></iframe>
      </div>
    </Modal>

    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" @click="handleSubmit">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { MACHINE_TYPES } from './constants'
import Util from '@/libs/util'
import UploadFile from './UploadFile'
import UploadImg from '@/components/UploadImg'
import { Message } from 'view-design'
const defaultFormData = {
  name: '',
  driverIds: '', // 驾乘人员
  projectName: '',
  workPointId: '', // 机械使用工点
  type: '', // 机械类型
  code: '', // 机械编号
  ownerShipType: '', // 机械所属
  specification: '', // 规格型号
  ownerShipName: '',
  enterTime: '', // 进场时间
  managerId: '',
  isSpecialEquipment: '0', // 是否特种机械
  // machineryAcceptanceStatus: '', // 现场安装机械验收情况
  // plannedRemovalTime: '', // 现场安装机械计划伐拆时间
  isPower: '0',
  inspectionFileList: [],
  certificateFileList: [],
  appraisalCertificateFileList: [],
  registrationCertificateFileList: [],
  equipmentPhotoFileList: [],
  manufacturer: '',
  manufacturerTel: '',
  maintainer: '',
  maintainerTel: '',
  maintainerDate: ''
}

export default {
  name: 'AddDialog',
  components: {
    UploadFile,
    UploadImg
  },
  data() {
    return {
      isModalShow: false,
      // loading: false,
      type: 'add', // add or edit
      formData: { ...defaultFormData },
      machineTypes: MACHINE_TYPES,
      drivers: [],
      workPoints: [],
      managers: [],
      previewVisible: false,
      previewUrl: '',
      isImage: false,
      specificalInfo: {}, // 存储机械类型相关的信息
      rules: {
        type: [{ required: true, message: '请选择机械类型', trigger: 'change' }],
        code: [{ required: true, message: '请输入机械编号', trigger: 'blur' }],
        ownerShipType: [{ required: true, message: '请选择机械所属', trigger: 'change' }],
        specification: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
        enterTime: [{ required: true, message: '请选择进场时间', trigger: 'change', type: 'date' }]
      },
      // 新增类型映射
      typeNameMap: {
        '1': '装载机',
        '2': '压路机',
        '3': '旋挖钻',
        '4': '挖掘机',
        '5': '汽车起重机',
        '6': '门式起重机',
        '7': '塔式起重机',
        '8': '随车起重机',
        '9': '洒水车',
        '10': '混凝土泵车',
        '11': '发电机',
        '12': '冲击钻',
        '13': '叉车'
      }
    }
  },
  computed: {
    title() {
      return this.type === 'add' ? '新增机械' : '编辑机械'
    }
  },
  watch: {
    'formData.code': function(val) {
      this.updateMachineName();
    },
    'formData.type': function(val) {
      this.updateMachineName();
    }
  },
  mounted() {
    this.getUserProject()
    this.getProjectInfo()
  },
  methods: {
    async open(type = 'add', data = null) {
      this.type = type
      this.isModalShow = true
      this.formData = { ...defaultFormData } // 先重置
      if (this.projectList && this.projectList.length > 0) {
        this.formData.projectName = this.projectList[0].platformProjectName || ''
      }
      // 获取下拉数据
      await Promise.all([
        this.getDrivers(),
        this.getManagers()
      ])

      if (type === 'edit' && data) {
        // 编辑时设置表单数据 
        // 查询接口 /mechanical/mechanical/queryById，用id查询
        const res = await Util.request('/mechanical/mechanical/queryById', { id: data.id }, 'get')
        if (res.data.success) {
          data = res.data.data
          console.log('编辑数据:', data)

          // 处理时间格式和null
          if (!data.enterTime) data.enterTime = ''
          if (data.enterTime) {
            data.enterTime = this.$moment(data.enterTime).format('YYYY-MM-DD HH:mm:ss')
            console.log('处理后的时间:', data.enterTime)
          }
          if (!data.maintainerDate) data.maintainerDate = ''
          if (data.maintainerDate) {
            data.maintainerDate = this.$moment(data.maintainerDate).format('YYYY-MM-DD')
          }
          if (!data.plannedRemovalTime) data.plannedRemovalTime = ''
          if (data.plannedRemovalTime) {
            data.plannedRemovalTime = this.$moment(data.plannedRemovalTime).format('YYYY-MM-DD')
          }

          // 处理文件列表
          const fileFields = [
            'inspectionFileList',
            'certificateFileList',
            'appraisalCertificateFileList',
            'registrationCertificateFileList',
            'equipmentPhotoFileList'
          ]
          fileFields.forEach(field => {
            if (data[field] && typeof data[field] === 'string') {
              data[field] = data[field].split(',').filter(Boolean)
            }
          })

          // 处理driverIds
          if (data.driverIds) {
            if (typeof data.driverIds === 'string') {
              data.driverIds = data.driverIds.split(',').filter(Boolean)
            } else if (!Array.isArray(data.driverIds)) {
              data.driverIds = [data.driverIds]
            }
            // 确保driverIds是字符串数组
            data.driverIds = data.driverIds.map(id => id.toString())
          } else {
            data.driverIds = []
          }

          // 处理managerId
          if (data.managerId) {
            data.managerId = data.managerId.toString()
          }

          // 处理单选按钮
          data.isPower = (data.isPower !== undefined && data.isPower !== null) ? data.isPower.toString() : '0'
          data.isSpecialEquipment = (data.isSpecialEquipment !== undefined && data.isSpecialEquipment !== null) ? data.isSpecialEquipment.toString() : '0'
          data.ownerShipType = (data.ownerShipType !== undefined && data.ownerShipType !== null) ? data.ownerShipType.toString() : '1'

          // 处理 specificalInfo
          if (data.specificalInfo) {
            try {
              this.specificalInfo = JSON.parse(data.specificalInfo)
            } catch (e) {
              console.error('解析 specificalInfo 失败:', e)
              this.specificalInfo = {}
            }
          }

          // 关键：合并赋值，保证所有属性都存在，避免响应式丢失
          this.formData = { ...defaultFormData, ...data }

          console.log('处理后的表单数据:', this.formData)
        }
      }
    },
    // 机械类型变更处理
    handleChange(value) {
      this.formData.type = value
      // 清空之前的特殊信息
      this.specificalInfo = {}
      
      // 根据机械类型设置默认值和机械名称
      switch (value) {
        case '1': // 装载机
          this.formData.name = '装载机'
          this.specificalInfo = {
            edzl: '', // 额定载重量
            xzjl: '', // 卸载距离
            zdyql: '', // 最大牵引力
            cdrl: '', // 铲斗容量
            fdjgl: '', // 发动机功率
            zxjd: '', // 转向角度
            xzgd: '', // 卸载高度
            zjzl: ''  // 整机重量
          }
          break
        case '2': // 压路机
          this.formData.name = '压路机'
          this.specificalInfo = {
            gzzl: '', // 工作质量
            xzsd: '', // 行走速度
            zdplzf: '', // 振动频率/振幅
            fdjgl: '', // 发动机功率
            nykd: '', // 碾压宽度
            glzjkd: '', // 钢轮直径/宽度
            ppnl: ''  // 爬坡能力
          }
          break
        case '3': // 旋挖钻
          this.formData.name = '旋挖钻'
          this.specificalInfo = {
            zkjz: '', // 钻孔直径
            zjtsl: '', // 主卷扬提升力
            zdkjzd: '', // 最大钻孔深度
            dllx: '', // 动力类型
            dltnj: '', // 动力头扭矩
            wdqxj: '', // 桅杆倾斜角度
            zjysc: ''  // 整机运输尺寸
          }
          break
        case '4': // 挖掘机
          this.formData.name = '挖掘机'
          this.specificalInfo = {
            zjzl: '', // 整机重量
            zdwjsd: '', // 最大挖掘深度
            zdwjgd: '', // 最大挖掘高度
            zdwjbj: '', // 最大挖掘半径
            yyxyl: '', // 液压系统压力
            cdrl: '', // 铲斗容量
            xzsd: '', // 行走速度
            dllx: '', // 动力类型
            fdjgl: '', // 发动机功率
            hzsd: ''  // 回转速度
          }
          break
        case '5': // 汽车起重机
          this.formData.name = '汽车起重机'
          this.specificalInfo = {
            zdqzl: '', // 最大起重量
            ztkj: '', // 支腿跨距
            dbjg: '', // 吊臂结构
            bc: '', // 臂长
            qsgd: '', // 起升高度
            dccznl: '', // 底盘承载能力
            zdhzbj: '', // 最大回转半径
            yyxylx: ''  // 液压系统类型
          }
          break
        case '6': // 门式起重机
          this.formData.name = '门式起重机'
          this.specificalInfo = {
            kd: '', // 跨度
            yxsd: '', // 运行速度
            qsgd: '', // 起升高度
            gdxh: '', // 轨道型号
            edqzl: '', // 额定起重量
            zljg: '', // 主梁结构
            czfs: ''  // 操作方式
          }
          break
        case '7': // 塔式起重机
          this.formData.name = '塔式起重机'
          this.specificalInfo = {
            dbc: '', // 大臂长
            xbc: '', // 小臂长
            zdqzlj: '', // 最大起重力矩
            tjgd: '', // 塔基高度
            zdzl: '', // 最大起重量
            gzfd: '', // 工作幅度
            jcczl: '', // 基础承载力
            tbg: '', // 塔臂高
            tmg: ''  // 起升速度
          }
          break
        case '8': // 随车起重机
          this.formData.name = '随车起重机'
          this.specificalInfo = {
            qsnl: '', // 起升能力
            zdhzbj: '', // 最大回转半径
            dccznl: '', // 底盘承载能力
            zdqzl: '', // 最大起重量
            yyxylx: '', // 液压系统类型
            xssd: '', // 行驶速度
            bc: '', // 臂长
            dbjg: ''  // 吊臂结构
          }
          break
        case '9': // 洒水车
          this.formData.name = '洒水车'
          this.specificalInfo = {
            sxrl: '', // 水箱容量
            ptsl: '', // 喷头数量
            pskd: '', // 喷洒宽度
            dczznl: '', // 底盘载重能力
            sbl: '', // 水泵流量
            sby: '', // 水泵压力
          }
          break
        case '10': // 混凝土泵车
          this.formData.name = '混凝土泵车'
          this.specificalInfo = {
            bjcd: '', // 臂架长度
            blbj: '', // 布料半径
            bsl: '', // 泵送量
            dccznl: '', // 底盘承载能力
            ssyl: '', // 输送压力
            yyxyl: '', // 液压系统压力
            ztkj: ''  // 支腿跨距
          }
          break
        case '11': // 发电机
          this.formData.name = '发电机'
          this.specificalInfo = {
            edgl: '', // 整机重量
            scdy: '', // 最大牵引力
            rllx: '', // 铲斗容量
            yxrl: '', // 发动机功率
            zysp: '', // 卸载高度
            lxyxsj: '', // 转速差
            zjzl: ''  // 整机重量
          }
          break
          case '12': 
          this.formData.name = '冲击钻'
          this.specificalInfo = {
            zl: '', // 整机重量
            jtlx: '', // 最大牵引力
            zs: '', // 铲斗容量
            srgl: '', // 发动机功率
            cjpl: '', // 卸载高度
            zkzj: '' // 转速差
          }
          break
          case '13': // 叉车
          this.formData.name = '叉车'
          this.specificalInfo = {
            xssd: '', // 整机重量
            hckd: '', // 最大牵引力
            hccd: '', // 铲斗容量
            dllx: '', // 发动机功率
            zwbj: '', // 卸载高度
            mjqj: '', // 转速差
            qsgd: '',  // 整机重量
            edzh: ''
          }
          break
      }
      
      // 更新 specificalInfo 字段
      this.formData.specificalInfo = JSON.stringify(this.specificalInfo)
    },
    // 获取当前用户所属项目，取platformProjectName字段 projectShortName
    getUserProject() {
      Util.request("/login/getUserProject", {}, "get")
        .then((res) => {
          if (res.data.success) {
            this.projectList = (res.data.data || {}).projectList || [];
            this.formData.projectName = this.projectList[0].platformProjectName || ''

            console.log('当前用户所属项目:', this.formData.projectName)
          }
        })
        .finally(() => {});
    },
    // 获取驾乘人员列表
    async getDrivers() {
      try {
        const res = await Util.request('/rosterPersonnel/getPage', {
          customQueryParams: { rosterType: '3' },
          page: { size: 10000, current: 1 }
        }, 'post')
        if (res.data.success) {
          this.drivers = res.data.data.records || []
          console.log('驾乘人员列表:', this.drivers)
        }
      } catch (error) {
        console.error('获取驾乘人员失败:', error)
      }
    },

    // 获取管理人员列表
    async getManagers() {
      try {
        const res = await Util.request('/rosterPersonnel/getPage', {
          customQueryParams: { rosterType: '0' },
          page: { size: 10000, current: 1 }
        }, 'post')
        if (res.data.success) {
          this.managers = res.data.data.records || []
          console.log('管理人员列表:', this.managers)
        }
      } catch (error) {
        console.error('获取管理人员失败:', error)
      }
    },
    // 获取项目信息
    async getProjectInfo() {
      try {
        const resp = await this.$Util.request('/index/project')
        if (resp.data.success) {
          this.project = resp.data.data
          console.log('this.project', this.project)
          console.log('this.project.projectId', this.project.projectId)
        } else {
          Message.error('获取项目信息失败')
        }
      } catch (error) {
        console.error('获取项目信息失败:', error)
        Message.error('获取项目信息失败')
      }
    },
    // 机械所属变更
    handleOwnershipChange(value) {
      if (value === '1') {
        // 自有时，公司名称自动填入施工单位名称
        Util.request(`/openapi/getAepTenantInfo?projectId=${this.project.projectId}`, {}, 'post')
        .then((res) => {
          if (res.data.success) {
            this.projectList = res.data.data
            this.formData.ownerShipName = this.projectList.designUnit || ''
            // console.log('this.projectList', this.projectList)
            // console.log('formData.ownerShipName:', this.formData.ownerShipName)
          }
        })
        .finally(() => {});
      } else {
        this.formData.ownerShipName = ''
      }
    },

    // 预览附件
    handlePreview(url) {
      this.isImage = /\.(jpg|jpeg|png|gif)$/i.test(url)
      this.previewUrl = url
      this.previewVisible = true
    },

    handleCancel() {
      this.isModalShow = false
    },

    async handleSubmit() {
      console.log('开始提交表单')
      this.$refs.form.validate(async valid => {
        console.log('表单验证结果:', valid)
        if (valid) {
          try {
            // 处理时间格式
            if (this.formData.enterTime) {
              this.formData.enterTime = this.$moment(this.formData.enterTime).format('YYYY-MM-DD HH:mm:ss')
            }
            if (this.formData.maintainerDate) {
              this.formData.maintainerDate = this.$moment(this.formData.maintainerDate).format('YYYY-MM-DD HH:mm:ss')
            }
            if (this.formData.plannedRemovalTime) {
              this.formData.plannedRemovalTime = this.$moment(this.formData.plannedRemovalTime).format('YYYY-MM-DD HH:mm:ss')
            }

            // 处理文件列表和driverIds
            const fileFields = [
              'inspectionFileList',
              'certificateFileList',
              'appraisalCertificateFileList',
              'registrationCertificateFileList',
              'equipmentPhotoFileList'
            ]

            // 处理文件列表，确保是数组格式
            fileFields.forEach(field => {
              if (this.formData[field]) {
                if (typeof this.formData[field] === 'string') {
                  this.formData[field] = this.formData[field].split(',').filter(Boolean)
                } else if (!Array.isArray(this.formData[field])) {
                  this.formData[field] = [this.formData[field]]
                }
              } else {
                this.formData[field] = []
              }
            })

            // 处理driverIds，确保是数组格式
            if (this.formData.driverIds) {
              if (typeof this.formData.driverIds === 'string') {
                this.formData.driverIds = this.formData.driverIds.split(',').filter(Boolean)
              } else if (!Array.isArray(this.formData.driverIds)) {
                this.formData.driverIds = [this.formData.driverIds]
              }
            } else {
              this.formData.driverIds = []
            }

            // 更新 specificalInfo
            this.formData.specificalInfo = JSON.stringify(this.specificalInfo)

            // 添加操作类型标识
            this.formData.operationType = this.type === 'add' ? 'add' : 'edit'

            console.log('this.type:', this.type)
            console.log('提交数据:', this.formData)
            
            // 修改接口地址
            const url = '/mechanical/mechanical/save'
            console.log('请求地址:', url)
            
            const res = await Util.request(url, this.formData, 'post')
            console.log('接口返回:', res)
            
            if (res.data.code === 'success') {
              this.$Message.success(this.title + '成功')
              this.isModalShow = false
              this.$emit('getData')
            }
          } catch (error) {
            console.error('保存失败:', error)
            this.$Message.error('保存失败：' + (error.message || '未知错误'))
          }
        }
      })
    },
    updateMachineName() {
      const typeName = this.typeNameMap[this.formData.type] || '';
      this.formData.name = typeName && this.formData.code
        ? `${this.formData.code}${typeName}`
        : '';
    }
  }
}
</script>

<style lang="less" scoped>
.form-container {
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  padding-right: 16px;
}
// 改变样式时间控件的样式 .ivu-btn-small
/deep/.ivu-btn-small {
  font-size: 12px;
  height: 24px;
  padding: 0 7px;
  border-radius: 2px;
}
/deep/.ivu-form .ivu-btn {
  min-width: 57px;
  margin-right: 5px;
}

.preview-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8f8f9;

  .preview-image {
    max-width: 100%;
    max-height: 100%;
  }

  .preview-pdf {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style> 