<template>
	<div class="grid-safe-container">
		<Card>
			<!-- 头部区域 -->
			<div class="container-title">
				<div class="status-legend">
					<span>
						<Badge status="success" text="所有网格点已完成" />
					</span>
					<span>
						<Badge status="warning" text="部分网格点已完成" />
					</span>
					<span>
						<Badge status="error" text="无网格点完成" />
					</span>
				</div>
				<Button type="default" @click="exportData" icon="md-log-out"
					>导出</Button
				>
			</div>
			<div class="header">
				<div class="header-left">
					<span>月份:</span>
					<DatePicker
						type="month"
						placeholder="请选择月份"
						style="width: 200px"
						v-model="selectedMonth"
						@on-change="handleMonthChange"
						:clearable="false"
					></DatePicker>
				</div>
				<div class="header-right">
					<div class="calendar-days">
						<div class="days-container">
							<Button
								icon="ios-arrow-back"
								shape="circle"
								size="small"
								class="nav-arrow left-arrow"
								@click="scrollLeft"
								:disabled="scrollPosition <= 0"
							></Button>
							<div class="days-scroll-wrapper" ref="daysWrapper">
								<div class="days-list" ref="daysList">
									<Button
										v-for="(day, index) in daysList"
										:key="day.day"
										:class="[
											'day-item',
											activeBtnIndex === index
												? 'click-active'
												: '',
										]"
										shape="circle"
										:disabled="!day.dis"
										:type="getDayType(day.finishStatus)"
										@click="handleDayClick(day, index)"
									>
										{{ day.day }}
									</Button>
								</div>
							</div>
							<Button
								icon="ios-arrow-forward"
								shape="circle"
								size="small"
								class="nav-arrow right-arrow"
								@click="scrollRight"
								:disabled="scrollPosition >= maxScrollPosition"
							></Button>
						</div>
					</div>
				</div>
			</div>
		</Card>
		<Card style="margin-top: 10px">
			<!-- 主体区域 -->
			<div class="main-content">
				<div class="left-menu">
					<NavItem
						ref="navItem"
						:navList="gridList"
						@on-change="handleGridChange"
					/>
				</div>
				<div class="right-content">
					<!-- 右侧内容区域，根据选择显示对应内容 -->
					<div class="empty-content">
						<p v-if="Object.keys(navList).length == 0">
							请选择左侧网格点查看详情
						</p>
						<p v-else-if="!navList.finishedFlag">暂无数据</p>
						<div class="table" v-else>
							<div class="table-header">
								<h2>{{ navList.gridName }}</h2>
								<div class="form-info">
									<span
										>作业网格：{{ navList.gridName }}</span
									>
									<span
										>填表时间：{{
											navList.createTime
										}}</span
									>
									<span>填表人：{{ navList.userName }}</span>
								</div>
							</div>
							<table>
								<thead>
									<tr>
										<td style="width: 50px">序号</td>
										<td>检查项目</td>
										<td>检查内容</td>
										<td>检查情况</td>
										<td>存在问题</td>
										<td>督促整改情况</td>
										<td>备注</td>
									</tr>
								</thead>
								<tbody
									v-for="(item, index) in navList.checkItems"
									:key="index"
								>
									<tr>
										<td
											:rowspan="
												String(
													item.checkChildItems.length
												) + 1
											"
										>
											{{ index + 1 }}
										</td>
										<td
											:rowspan="
												String(
													item.checkChildItems.length
												) + 1
											"
										>
											{{ item.name }}
										</td>
									</tr>
									<tr
										v-for="(
											item1, index1
										) in item.checkChildItems"
										:key="index1"
									>
										<td>{{ item1.name }}</td>
										<td>
											{{
												item1.gridSecurityLogRelation
													? item1
															.gridSecurityLogRelation
															.checkDesc
													: ""
											}}
										</td>
										<td>
											{{
												item1.gridSecurityLogRelation
													? item1
															.gridSecurityLogRelation
															.problemDesc
													: ""
											}}
										</td>
										<td>
											{{
												item1.gridSecurityLogRelation
													? item1
															.gridSecurityLogRelation
															.improveDesc
													: ""
											}}
										</td>
										<td>
											{{
												item1.gridSecurityLogRelation
													? item1
															.gridSecurityLogRelation
															.remark
													: item1.remark || ""
											}}
										</td>
									</tr>
								</tbody>
								<tbody>
									<tr>
										<td colspan="7">
											<div class="person-box">
												<div class="name">图片</div>
												<div
													v-if="imgList.length > 0"
													class="img-list"
												>
													<div
														v-for="(
															item, index
														) in imgList"
														:key="index"
														@click="
															checkSignImg(
																imgList,
																index
															)
														"
														class="img-box"
													>
														<img :src="item" />
													</div>
												</div>
											</div>
										</td>
									</tr>
									<tr>
										<td colspan="7">
											<div class="person-box">
												<div class="name">人员签字</div>
												<div
													v-if="signList.length > 0"
													class="img-list"
												>
													<div
														v-for="(
															item, index
														) in signList"
														:key="index"
														@click="
															checkSignImg(
																signList,
																index
															)
														"
														class="img-box"
													>
														<img :src="item" />
													</div>
												</div>
											</div>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</Card>
		<ImgModal ref="imgModalRef" />
	</div>
</template>

<script>
import NavItem from "@/components/NavItem/gridIndex.vue";
import Util from "@/libs/util";
import ImgModal from "../managePenetration/classMeeting/components/imgModal.vue";
export default {
	name: "GridSafe",
	components: {
		NavItem,
		ImgModal,
	},
	data() {
		return {
			selectedMonth: new Date(),
			activeMenu: "grid1",
			scrollPosition: 0,
			maxScrollPosition: 0,
			scrollStep: 200, // 每次滚动的距离
			gridList: [],
			daysList: [],
			activeBtnIndex: "",
			navList: {},
			monthData: "",
			title: "",
			currentSelectedDate: "", // 添加当前选中的日期
			signList: [],
			imgList: [],
		};
	},
	mounted() {
		this.$nextTick(() => {
			this.calculateScrollLimits();
			this.handleMonthChange(
				this.selectedMonth.getFullYear() +
					"-" +
					(this.selectedMonth.getMonth() + 1)
			);
		});
		Util.request("/index/project").then((resp) => {
			this.title = resp.data.data.platformProjectName;
		});
		// 监听窗口大小变化
		window.addEventListener("resize", this.calculateScrollLimits);
	},
	beforeDestroy() {
		window.removeEventListener("resize", this.calculateScrollLimits);
	},
	methods: {
		checkSignImg(list, index) {
			this.$refs.imgModalRef.show(list, index);
		},
		// 查询月度数据（只查询日期和状态）
		initRequest(date) {
			this.monthData = date;
			Util.request("/gird/gridSecurityLog/queryByMonth", {
				month: date,
			}).then((res) => {
				this.reset();
				let today = new Date().getTime();
				this.daysList = res.data.data.map((i) => {
					let date = new Date(i.date).getTime();
					let option = {
						...i,
						dis: date - today >= 0 ? false : true,
					};
					return option;
				});
				this.$nextTick(() => {
					this.calculateScrollLimits();
					this.updateScrollPosition();

					// 自动查询当前天数的数据
					if (this.daysList && this.daysList.length > 0) {
						// 获取当前天数 格式d
						let currentDate = new Date();
						let currentDay = currentDate.getDate();
						this.activeBtnIndex = currentDay - 1;
						this.queryDayData(this.daysList[currentDay - 1].date);
					}
				});
			});
		},

		// 查询具体某天的数据
		queryDayData(date) {
			this.currentSelectedDate = date;
			Util.request("/gird/gridSecurityLog/queryByDate", {
				date: date,
			})
				.then((res) => {
					// 假设返回的数据结构和月度查询一样，包含gridSecurityLogList
					if (res.data.data && res.data.data.gridSecurityLogList) {
						this.gridList = res.data.data.gridSecurityLogList;
						this.$nextTick(() => {
							this.$refs.navItem.handleFirstClick();
						});
					} else {
						this.gridList = [];
					}
				})
				.catch((error) => {
					console.error("查询天数据失败:", error);
					this.$Message.error("查询数据失败");
					this.gridList = [];
				});
		},
		// 导出数据
		exportData() {
			if (!this.monthData) {
				this.$Message.error("请选择月份");
				return;
			}
			Util.requestGet(
				"/gird/gridSecurityLog/exportByMonth?month=" + this.monthData,
				{},
				"blob"
			).then((res) => {
				// zip
				let blob = new Blob([res.data], {
					type: "application/zip;charset=utf-8",
				});
				let url = window.URL.createObjectURL(blob);
				let a = document.createElement("a");
				a.download =
					this.title +
					this.monthData.split("-")[1] +
					"月" +
					"网格安全员日志台账" +
					Util.formatDate(new Date(), "yyyy-MM-dd") +
					".zip";
				a.href = url;
				a.target = "_blank";
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
			});
		},
		handleMonthChange(date) {
			this.scrollPosition = 0;
			this.initRequest(date);
		},
		handleDayClick(day, index) {
			this.reset();
			this.activeBtnIndex = index;

			// 直接使用day.date，因为数据结构中包含date字段 (yyyy-MM-dd格式)
			this.queryDayData(day.date);
		},
		handleMenuSelect(name) {
			this.activeMenu = name;
		},
		getDayType(finishStatus) {
			// 根据完成状态返回按钮类型
			// 0-未完成 1-已完成 2-部分完成
			switch (finishStatus) {
				case 1:
					return "success"; // 已完成
				case 2:
					return "warning"; // 部分完成
				case 0:
				default:
					return "error"; // 未完成
			}
		},
		scrollLeft() {
			console.log("点击左箭头");
			this.scrollPosition = Math.max(
				0,
				this.scrollPosition - this.scrollStep
			);
			this.updateScrollPosition();
		},
		scrollRight() {
			console.log("点击右箭头");
			this.scrollPosition = Math.min(
				this.maxScrollPosition,
				this.scrollPosition + this.scrollStep
			);
			this.updateScrollPosition();
		},
		updateScrollPosition() {
			console.log("更新滚动位置:", this.scrollPosition);
			if (this.$refs.daysList) {
				this.$refs.daysList.style.transform = `translateX(-${this.scrollPosition}px)`;
				console.log(
					"应用transform:",
					`translateX(-${this.scrollPosition}px)`
				);
			} else {
				console.log("daysList ref 不存在");
			}
		},
		calculateScrollLimits() {
			this.$nextTick(() => {
				if (this.$refs.daysList && this.$refs.daysWrapper) {
					const listWidth = this.$refs.daysList.scrollWidth;
					const wrapperWidth = this.$refs.daysWrapper.clientWidth;
					this.maxScrollPosition = Math.max(
						0,
						listWidth - wrapperWidth
					);

					console.log("计算滚动限制:", {
						listWidth,
						wrapperWidth,
						maxScrollPosition: this.maxScrollPosition,
					});

					// 如果当前滚动位置超过最大值，调整到最大值
					if (this.scrollPosition > this.maxScrollPosition) {
						this.scrollPosition = this.maxScrollPosition;
						this.updateScrollPosition();
					}
				} else {
					console.log("refs 不存在:", {
						daysList: !!this.$refs.daysList,
						daysWrapper: !!this.$refs.daysWrapper,
					});
				}
			});
		},
		handleGridChange(item) {
			console.log(item, "item");
			this.signList = item.userSign ? [item.userSign] : [];
			this.imgList = item.logFiles ? item.logFiles.split(",") : [];
			this.navList = item;
		},
		reset() {
			this.navList = {};
			this.gridList = [];
			this.activeBtnIndex = "";
			this.currentSelectedDate = "";
		},
	},
};
</script>

<style lang="less" scoped>
.form-info {
	display: flex;
	justify-content: space-between;
}
/deep/ button {
	background-color: transparent !important;
	color: black !important;
}
.table {
	width: 100%;
	height: 100%;
	padding: 0 10px;
	overflow: auto;

	.table-header {
		width: 95%;
		margin: 0 auto;
		margin-bottom: 10px;
	}

	table {
		// 合并线
		border-collapse: collapse;
		width: 100%;
		td {
			border: 1px solid #dcdee2;
			padding: 5px;
		}
		th {
			border: 1px solid #dcdee2;
			padding: 5px;
		}
	}
}
.person-box {
	display: flex;
	.name {
		width: 100px;
	}
}
.img-list {
	display: flex;
	align-items: center;
	gap: 8px;
	.img-box {
		width: 40px;
		height: 40px;
		cursor: pointer;
		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
}
// 隐藏table滚动条
.table::-webkit-scrollbar {
	display: none;
}

.container-title {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
}

.status-legend {
	span {
		margin-right: 10px;
	}
}

.grid-safe-container {
	height: 100%;
	.header {
		display: flex;
		align-items: center;

		.header-left {
			width: 270px;
			display: flex;
			align-items: center;
			gap: 10px;
			margin-right: 20px;
		}

		.header-right {
			width: calc(~"100% - 270px");

			.calendar-days {
				.days-container {
					display: flex;
					align-items: center;
					position: relative;

					.nav-arrow {
						z-index: 10;
						background: #fff;
						border: 1px solid #dcdee2;

						&.left-arrow {
							margin-right: 8px;
						}

						&.right-arrow {
							margin-left: 8px;
						}

						&:hover:not(:disabled) {
							border-color: #57a3f3;
							color: #57a3f3;
						}
					}

					.days-scroll-wrapper {
						flex: 1;
						overflow: hidden;
						position: relative;
						background-color: #f7f8fa;
						padding: 10px 0;

						.days-list {
							display: flex;
							gap: 8px;
							transition: transform 0.3s ease;
							white-space: nowrap;

							.day-item {
								width: 32px;
								height: 32px;
								padding: 0;
								display: flex;
								align-items: center;
								justify-content: center;
								flex-shrink: 0;

								&:hover {
									cursor: pointer;
								}
							}

							.day-item.click-active {
								background-color: #bbcdf9 !important;
							}
						}
					}
				}
			}
		}
	}

	.main-content {
		display: flex;
		height: calc(~"100vh - 280px");

		.left-menu {
			width: 200px;
			border-right: 1px solid #dcdee2;
			margin-right: 20px;
			height: 100%;
			overflow-y: auto;
		}

		.right-content {
			width: calc(~"100% - 200px");
			height: 100%;
			border-radius: 4px;
			display: flex;
			align-items: center;
			justify-content: center;

			.empty-content {
				width: 100%;
				height: 100%;
				text-align: center;
				font-size: 14px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}
</style>
