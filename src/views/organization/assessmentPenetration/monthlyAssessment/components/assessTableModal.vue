<template>
	<Modal title="考核表" v-model="visible" :footer-hide="true" width="75%">
		<div v-if="detailObj && detailObj.id" class="scroll-box">
			<div class="title-box">
				{{ assessType[detailObj.assessType] }}考核表
			</div>
			<div class="info-box">
				<div class="info-item">考核对象:{{ detailObj.assessUserName }}</div>
				<div class="info-item">考核月份:{{ detailObj.assessMonth }}</div>
			</div>
			<div class="table-main">
				<table>
					<tr>
						<th style="width: 50px">序号</th>
						<th style="width: 100px">考核指标</th>
						<th style="width: 100px">考核项目</th>
						<th>考核内容</th>
						<th style="width: 80px">配分标准</th>
						<th style="width: 80px">扣分分值</th>
						<th style="width: 80px">得分分值</th>
						<th style="width: 100px">备注</th>
					</tr>
					<tr v-for="(item, index) in tableList" :key="index">
						<td>{{ index + 1 }}</td>
						<td v-if="item.num" :rowspan="item.num">
							{{ item.assessTarget }}
						</td>
						<td v-if="item.num1" :rowspan="item.num1">
							{{ item.assessEntry }}
						</td>
						<td>{{ item.assessContent }}</td>
						<td>
							{{ item.scoreStandard === 0 ? '-' : (item.assessTarget === '加分项' ? '+' + item.scoreStandard : item.scoreStandard) }}
						</td>
						<td>{{ !item.recordDeductScore ? item.recordDeductScore : ('-' + item.recordDeductScore) }}</td>
						<td>{{ item.recordActualScore }}</td>
						<td>{{ item.recordRemark }}</td>
					</tr>
					<tr>
						<td colspan="4" style="text-align: center;">合计</td>
						<td>{{ detailObj.totalScore }}</td>
						<td>{{ !detailObj.deductScore ? detailObj.deductScore : (detailObj.deductScore) }}</td>
						<td>{{ detailObj.actualScore }}</td>
						<td></td>
					</tr>
					<tr>
						<td colspan="8">
							<div class="assess-result">
								<div class="name">考核结果：</div>
								<span v-if="detailObj.assessResult === 0" class="result1">优秀</span>
								<span v-else-if="detailObj.assessResult === 1" class="result2">合格</span>
								<span v-else-if="detailObj.assessResult === -1" class="result3">不合格</span>
							</div>
							<div class="info-box">
								<div class="sign-item">考核组员签字:
									<img v-if="detailObj.assessGroupSign" :src="detailObj.assessGroupSign" @click="checkSignImg" class="sign-img" alt="签字" /></div>
								<div class="info-item">{{ detailObj.finishTime ? $Util.formatDate(detailObj.finishTime, 'yyyy-MM-dd') : '' }}</div>
							</div>
						</td>
					</tr>
				</table>
			</div>
		</div>
		<ImgModal ref="imgModalRef" />
	</Modal>
</template>
<script>
import ImgModal from "@/views/organization/managePenetration/classMeeting/components/imgModal.vue";
export default {
	name: "assessTableModal",
	components: {
		ImgModal,
	},
	props: {},
	data() {
		return {
			visible: false,
			detailObj: {},
			tableList: [],
			assessType: {
				1: '网格员',
				2: '安监专务',
				3: '施工队伍'
			},
		};
	},
	methods: {
		show(data) {
			this.visible = true;
			this.$Util
				.request("/assessTable/getOne/" + data.id, {}, "get")
				.then((res) => {
					this.detailObj = res.data.data;
					this.tableList = this.getTableData(
						res.data.data.assessTableRecordVoList
					);
				});
		},
		getTableData(list) {
			let str = "";
			let str1 = ''
			list.forEach((item) => {
				if (str !== item.assessTarget) {
					item.num = 0;
					list.forEach((k) => {
						if (k.assessTarget === item.assessTarget) {
							item.num++;
						}
					});
					str = item.assessTarget;
				}
				if (str1 !== item.assessEntry) {
					item.num1 = 0;
					list.forEach((k) => {
						if (k.assessEntry === item.assessEntry) {
							item.num1++;
						}
					});
					str1 = item.assessEntry;
				}
			});
			console.log(list);
			return list;
		},
		checkSignImg() {
			this.$refs.imgModalRef.show([this.detailObj.assessGroupSign], 0);
		},
		close() {
			this.visible = false;
		},
	},
};
</script>
<style lang="less" scoped>
@borderColor: #dcdee2;
.title-box {
	font-size: 20px;
	font-weight: bold;
	margin-bottom: 10px;
	text-align: center;
}
.info-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 10px;
}
.sign-item{
	display: flex;
	align-items: center;
}
.sign-img{
	width: 40px;
	height: 40px;
	margin-left: 4px;
	cursor: pointer;
}
.table-main {
	width: 100%;
	table {
		width: 100%;
		border: 1px solid @borderColor;
		border-collapse: collapse;
		th,
		td {
			border-bottom: 1px solid @borderColor;
			border-right: 1px solid @borderColor;
			padding: 10px;
		}
	}
	.ivu-checkbox-wrapper:last-child {
		margin-right: 0;
	}
}
.assess-result{
	display: flex;
	align-items: center;
	.name{
		margin-right: 4px;
	}
	.result1{
		color: #2d8cf0;
	}
	.result2{
		color: #19be6b;
	}
	.result3{
		color: #ed4014;
	}
}
</style>
