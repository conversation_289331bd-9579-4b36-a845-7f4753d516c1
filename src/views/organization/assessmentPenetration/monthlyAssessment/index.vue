<template>
	<div class="monthly-assessment">
		<Row :gutter="8">
			<Col span="6">
				<ContentCard>
					<div class="monthly-assessment-left">
						<Tree :data="monthList" @on-select-change="changeMonth"></Tree>
					</div>
				</ContentCard>
			</Col>
			<Col span="18">
				<ContentCard>
					<BaseForm
						:model="searchForm"
						:label-width="90"
						:init-form-data="true"
						@handleSubmit="handleSearch"
						@handleReset="handleReset"
					>
						<template #formitem>
							<FormItem label="考核对象" prop="assessUser">
								<Input
									v-model="searchForm.assessUser"
									placeholder="请输入"
									clearable
								></Input>
							</FormItem>
							<FormItem label="考核类型" prop="assessType">
								<Select
									v-model="searchForm.assessType"
									clearable
								>
									<Option
										v-for="(item, index) in assessType"
										:key="index"
										:value="index"
										>{{ item }}
									</Option>
								</Select>
							</FormItem>
							<FormItem label="考核状态" prop="assessStatus">
								<Select
									v-model="searchForm.assessStatus"
									clearable
								>
									<Option
										v-for="(item, index) in statusList"
										:key="index"
										:value="item.value"
										>{{ item.text }}
									</Option>
								</Select>
							</FormItem>
						</template>
					</BaseForm>
					<div class="action-box">
						<Button type="primary" @click="handleExport">导出</Button>
					</div>
					<!-- 数据表格 -->
					<EmpTable
						:columns="columns"
						url="/assessTable/selectTablePage"
						ref="table"
						@update:changePage="()=>{selectedList=[]}"
						@update:pageSize="()=>{selectedList=[]}"
						@on-selection-change="getSelectionChange"
						@on-sort-change="handleSortChange"
					>
						<template slot="assessType" slot-scope="{ row }">
							{{ assessType[row.assessType] }}
						</template>
						<template slot="assessResult" slot-scope="{ row }">
							{{(row.assessResult || row.assessResult === 0) ? assessResult[row.assessResult.toString()] : ''}}
						</template>
						<template slot="actualScore" slot-scope="{ row }">
							{{ row.actualScore }}
						</template>
						<template slot="assessTable" slot-scope="{ row }">
							<linkBtn @click="handleAssessTable(row)">{{ row.assessUserName }}考核表{{ row.assessMonth }}</linkBtn>
						</template>
						<template slot="statusSlot" slot-scope="{ row }">
							<!-- 待自查、签字中、已完成 -->
							<Tag
								v-if="row.assessStatus == 2"
								color="success"
								>已完成</Tag
							>
							<Tag
								v-else-if="row.assessStatus == 1"
								color="primary"
								>签字中</Tag
							>
							<Tag v-else color="warning">待自查</Tag>
						</template>
					</EmpTable>
				</ContentCard>
			</Col>
		</Row>
		<assessTableModal ref="assessTableModal"></assessTableModal>
	</div>
</template>
<script>
import assessTableModal from './components/assessTableModal.vue'
const defaultSearch = {
	assessType: '',
	assessUser: '',
	assessMonth: '',
	assessStatus: ''
};
export default {
	components: {
		assessTableModal
	},
	data() {
		return {
			monthList: [],
			searchForm: Util.objClone(defaultSearch),
			columns: [
				{ type: "selection", width: 40, align: "center" },
				{ title: "考核月份", key: "assessMonth", tooltip: true, width: 100, sortable: 'custom' },
				{ title: "考核对象", key: "assessUserName", tooltip: true },
				{ title: "考核类型", slot: "assessType", tooltip: true },
				{ title: "考核分数", slot: "actualScore", key: "actualScore", tooltip: true, sortable: 'custom' },
				{ title: "考核结果", slot: "assessResult", tooltip: true },
				{ title: "考核表", slot: "assessTable", tooltip: true },
				{ title: "状态", slot: "statusSlot", width: 80 },
			],
			assessType: {
				1: '网格安全员',
				2: '安监专务',
				3: '施工队伍'
			},
			assessResult: {
				'0': '优秀',
				'1': '合格',
				'-1': '不合格'
			},
			selectedList: [],
			statusList: [
				{value: 0, text: '待自查'},
				{value: 1, text: '签字中'},
				{value: 2, text: '已完成'}
			],
		};
	},
	mounted() {
		this.getAssessMonthList()
		this.handleSearch()
	},
	methods: {
		handleSortChange(column, key, order) {
			this.searchForm.sort = column.key
			this.searchForm.order = order
			this.handleSearch()
		},
		handleSearch() {
			this.$refs.table.search(this.searchForm);
		},
		handleReset() {
			this.searchForm = Util.objClone(defaultSearch);
			this.handleSearch();
		},
		handleAssessTable(row) {
			this.$refs.assessTableModal.show(row)
		},
		handleExport() {
			if (this.selectedList.length === 0) {
				this.$Message.warning('请选择要导出的数据');
				return;
			}
			let ids = this.selectedList.map(k => k.id).join(',')
			const windowToken = Util.getWindowToken()
			let name = localStorage.getItem(windowToken + 'projectName')
			name += '考核表-'
			name += this.$Util.formatDate(new Date(), "yyyy-MM-dd") + '.zip'
			this.$Util.exportData('/assessTable/exportAssessTable/' + ids, {}, name, 'get')
		},
		getSelectionChange(rows) {
			this.selectedList = rows;
		},
		// 选择月份
		changeMonth(arr, row) {
			// 先清除所有节点的选中状态
			this.clearAllSelected(this.monthList);
			// 设置当前节点为选中状态
			row.selected = true;
			
			if (row.value === 'all') {
				this.searchForm.assessMonth = ''
			} else {
				this.searchForm.assessMonth = row.value
			}
			this.handleSearch()
		},
		// 得到考核月份
		getAssessMonthList() {
			this.$Util.request('/assessTable/getAssessMonths', {}, 'post').then(res=>{
				let list = res.data.data.map(k => {
					return {
						value: k,
						title: k,
						selected: false
					}
				})
				this.monthList = [
					{
						value: 'all',
						title: '全部',
						expand: true,
						selected: true, // 默认选中全部
						children: list
					}
				]
			})
		},
		// 清除所有节点的选中状态
		clearAllSelected(nodes) {
			nodes.forEach(node => {
				node.selected = false;
				if (node.children && node.children.length > 0) {
					this.clearAllSelected(node.children);
				}
			});
		},
	},
};
</script>
<style lang="less" scoped>
.monthly-assessment {
	.content-card{
		height: 100%;
		/deep/ .ivu-card-body{
			height: 100%;
		}
	}
	.monthly-assessment-left {
		height: 100%;
		overflow-y: auto;
	}
	.action-box {
		margin-bottom: 8px;
	}
}
</style>
