<template>
  <div class="hazard-management" style="height: 100vh; padding: 16px;">
    <!-- 头部统计板块 -->
     <div class="header">
      <Row :gutter="16" style="margin-bottom: 16px; height: 100%; width: 100%;">
      <!-- 隐患级别统计 -->
      <Col span="8" style="height: 100%;">
        <Card style="height: 100%">
          <div slot="title">隐患级别统计</div>
          <div class="level-stats">
            <div class="stat-item">
              <span class="count blue">{{ warnInfoTotalNum }}</span>
              <span class="label"></span>
              <span class="percentage">全部隐患</span>
            </div>
            <div class="stat-item" v-for="(item, index) in warnInfoText" :key="index">
              <span :class="['count', item.color]">{{ item.sum }}</span>
              <span class="label">{{ item.levelDesc }}</span>
              <span class="percentage">{{ item.sumPercent }}</span>
            </div>
          </div>
        </Card>
      </Col>

      <!-- 隐患状态统计 -->
      <Col span="8" style="height: 100%;">
        <Card style="height: 100%;">
          <div slot="title">隐患状态统计</div>
          <div class="status-stats">
            <div class="stat-item" v-for="(item, index) in warnInfoStatus" :key="index">
              <span :class="['count', item.color]">{{ item.sum }}</span>
              <span class="label">{{ item.statusDesc }}</span>
              <span class="percentage">{{ item.statusPercent }}</span>
            </div>
          </div>
        </Card>
      </Col> 

      <!-- 隐患数量按月统计 -->
      <Col span="8" style="height: 100%;" class="month-chart-container">
        <Card style="height: 100%;">
          <div slot="title">隐患数量按月统计</div>
          <div class="chart-container">
            <div ref="monthChart" id="monthChart" style="height: 100%; width: 100%;"></div>
          </div>
        </Card>
      </Col>
    </Row>
     </div>
    

    <!-- 筛选条件 -->
    <Card style="margin-bottom: 16px;">
      <Row :gutter="16">
        <Col span="5">
          <div class="filter-item">
            <label>隐患类别</label>
            <Select v-model="filterForm.troubleType" placeholder="全部">
              <Option value="" v-for="(item, index) in warnClassify" :key="index" :value="item">{{ item }}</Option>
            </Select>
          </div>
        </Col>
        <Col span="5">
          <div class="filter-item">
            <label>隐患级别</label>
            <Select v-model="filterForm.troubleLevel" placeholder="全部">
              <Option value="0">一般</Option>
              <Option value="1">突出</Option>
              <Option value="2">重大</Option>
            </Select>
          </div>
        </Col>
        <Col span="5">
          <div class="filter-item">
            <label>状态</label>
            <Select v-model="filterForm.troubleStatus" placeholder="全部">
              <Option value="0">待整改</Option>
              <Option value="1">待复查</Option>
              <Option value="2">待销号</Option>
              <Option value="3">已销号</Option>
            </Select>
          </div>
        </Col>
        <Col span="5">
          <div class="filter-item">
            <label>发布日期</label>
            <DatePicker 
              v-model="filterForm.publishDate" 
              type="daterange" 
              placeholder="开始日期 - 结束日期"
              style="width: 100%"
            />
          </div>
        </Col>
        <Col span="4">
          <div class="filter-item">
            <label>整改是否逾期</label>
            <Select v-model="filterForm.isOverdue" placeholder="全部">
              <Option value="0">未超期</Option>
              <Option value="1">已超期</Option>
            </Select>
          </div>
        </Col>
      </Row>
      <Row>
        <Col>
          <div class="filter-actions">
            <Button type="primary" @click="handleSearch">查询</Button>
            <Button @click="handleReset" style="margin-left: 8px;">重置</Button>
          </div>
        </Col>
      </Row>
    </Card>

    <!-- 表格 -->
    <Card>
      <div slot="title">
        <span>已选择：{{ selectedRows.length }} 个</span>
        <Button size="small" style="margin-left: 16px;" @click="handleExport">导出</Button>
      </div>
      <Table 
        :columns="columns" 
        :data="tableData" 
        :loading="loading"
        @on-selection-change="handleSelectionChange"
        border
        stripe
      >
        <template slot-scope="{ row }" slot="troubleLevel">
          <Tag :color="getLevelColor(row.troubleLevel)">{{ getLevelText(row.troubleLevel) }}</Tag>
        </template>
        <template slot-scope="{ row }" slot="troubleStatus">
          <Tag :color="getStatusColor(row.troubleStatus)">{{ getStatusText(row.troubleStatus) }}</Tag>
        </template>
        <template slot-scope="{ row }" slot="media">
          <div v-if="row.troubleFiles">
            <a 
              v-for="(item, index) in getMediaFiles(row.troubleFiles)" 
              :key="index"
              @click="previewMedia(item)"
              style="margin-right: 8px; color: #2d8cf0;"
            >
              {{ item.name }}
            </a>
          </div>
          <span v-else>-</span>
        </template>
        <template slot-scope="{ row }" slot="rectifyFiles">
          <a 
              v-for="(item, index) in getMediaFiles(row.rectifyFiles)" 
              :key="index"
              @click="previewMedia(item)"
              style="margin-right: 8px; color: #2d8cf0;"
            >
              {{ item.name }}
            </a>
        </template>
        <template slot-scope="{ row }" slot="action">
          <Button type="text" size="small" @click="handleDetail(row)">详情</Button>
          <Button type="text" size="small" @click="handleEdit(row)">删除</Button>
        </template>
      </Table>
      
      <!-- 分页 -->
      <div style="margin-top: 16px; text-align: right;">
        <Page 
          :current="pagination.current" 
          :total="pagination.total" 
          :page-size="pagination.pageSize"
          :page-size-opts="[10, 20, 50, 100]"
          show-sizer
          show-elevator
          show-total
          @on-change="handlePageChange"
          @on-page-size-change="handlePageSizeChange"
        />
      </div>
    </Card>
    
    <!-- 隐患详情弹窗 -->
    <HazardDetailModal v-model="detailModalVisible" :hazard-data="currentHazardData" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import HazardDetailModal from '@/components/HazardDetailModal.vue';
import Util from '@/libs/util';

export default {
  name: 'HazardManagement',
  components: {
    HazardDetailModal
  },
  data() {
    return {
      // 筛选表单
      filterForm: {
        troubleType: '',
        troubleLevel: '',
        troubleStatus: '',
        publishDate: [],
        isOverdue: ''
      },
      // 隐患统计左侧
      warnInfoText: [],
      warnInfoStatus: [],
      warnInfoMonthCharts: [],
      warnInfoTotalNum: 0, 
      // 隐患分类
      warnClassify: ['基础工程', 
      '混凝土工程',
      '彻体工程', 
      '安全生产', 
      '层面防水工程', 
      '施工用电', 
      '起重机械设备', 
      '施工机具', 
      '文明施工'],
      // 表格数据
      tableData: [],
      loading: false,
      selectedRows: [],
      // 项目名称
      title: '',
      // 分页
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
            // 表格列配置
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '编号',
          key: 'troubleNo',
          width: 120,
          align: 'center'
        },
        {
          title: '隐患类别',
          key: 'troubleType',
          width: 120,
          align: 'center'
        },
        {
          title: '隐患级别',
          key: 'troubleLevel',
          width: 100,
          align: 'center',
          slot: 'troubleLevel'
        },
        {
          title: '隐患内容',
          key: 'troubleContent',
          width: 200,
          ellipsis: true
        },
        {
          title: '整改是否逾期',
          key: 'isOverdue',
          width: 200,
          align: 'center',
          render: (h, params) => {
            return h('span', params.row.isOverdue == 0 ? '未超期' : '已超期')
          }
        },
        {
          title: '整改要求',
          key: 'rectifyRequire',
          width: 200,
          ellipsis: true
        },
        {
          title: '现场图片或视频',
          key: 'troubleFiles',
          width: 150,
          align: 'center',
          slot: 'media'
        },
        {
          title: '整改后图片或视频',
          key: 'rectifyFiles',
          width: 150,
          align: 'center',
          slot: 'rectifyFiles'
        },
        {
          title: '整改回复',
          key: 'rectifyUserName',
          width: 150,
          ellipsis: true
        },
        {
          title: '复查结果',
          key: 'troubleStatus',
          width: 100,
          align: 'center',
          slot: 'troubleStatus'
        },
        {
          title: '检查人',
          key: 'checkUserName',
          width: 100,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 120,
          align: 'center',
          slot: 'action',
          fixed: 'right'
        }
      ],
        myCharts: null,
        // 详情弹窗相关
        detailModalVisible: false,
        currentHazardData: {}
      }
      
    },
  mounted() {
    this.loadData();
    this.initRequest();
  },
  methods: {
    initRequest() {
      this.loading = true
      console.log(this.filterForm.publishDate, '42379084')
      
      // 处理时间范围数据
      const requestParams = { ...this.filterForm }
      
      // 如果选择了时间范围，转换为开始时间和结束时间
      if (requestParams.publishDate && Array.isArray(requestParams.publishDate) && requestParams.publishDate.length === 2) {
        const [startDate, endDate] = requestParams.publishDate
        
        // 格式化开始时间（当天 00:00:00）
        if (startDate) {
          requestParams.startTime = this.formatDateTime(startDate, 'start')
        }
        
        // 格式化结束时间（当天 23:59:59）
        if (endDate) {
          requestParams.endTime = this.formatDateTime(endDate, 'end')
        }
        
        console.log('转换后的时间范围:', {
          原始数据: requestParams.publishDate,
          开始时间: requestParams.startTime,
          结束时间: requestParams.endTime
        })
      }
      
      // 移除原始的时间范围字段
      delete requestParams.publishDate
      
      Util.request('/hiddenTrouble/getPage', {
        page: {
          current: this.pagination.current,
          size: this.pagination.pageSize
        },
        customQueryParams: requestParams,
        sort: {}
      }, 'POST').then(res => {
        if (res.data.code == 'success') {
          console.log(res.data.data);
          this.tableData = res.data.data.records;
          this.pagination.total = res.data.data.total;
        }
      }).finally(() => {
        this.loading = false
      })
      Util.request("/index/project").then((resp) => {
        let id = resp.data.data.id;
        this.title = resp.data.data.platformProjectName
        Util.request('/hiddenTrouble/troubleStatistic/' + id).then(res => {
          if (res.data.code == 'success') {
            /**
             * cancelSum: 0
              level: 2
              levelDesc: "重大"
              sum: 2
              sumPercent: "16.7%"
             */
            if (res.data.data.levelList.length > 0) {
              this.warnInfoText = res.data.data.levelList.map(i => {
                if (i.levelDesc == '一般') {
                  i.color = 'green';
                } else if (i.levelDesc == '突出') {
                  i.color = 'orange';
                } else if (i.levelDesc == '重大') {
                  i.color = 'red';
                }
                return i;
              });
            }
            // 全部隐患
            this.warnInfoTotalNum = res.data.data.total;
            /**
             * status: 1
             * statusDesc :"待复查"
             * statusPercent: "0.0%"
                sum: 0
             */
            if (res.data.data.statusList.length > 0) {
              this.warnInfoStatus = res.data.data.statusList.map(i => {
                if (i.statusDesc == '待复查') {
                  i.color = 'orange';
                } else if (i.statusDesc == '待整改') {
                  i.color = 'red';
                } else if (i.statusDesc == '待销号') {
                  i.color = 'yellow';
                } else if (i.statusDesc == '已销号') {
                  i.color = 'green';
                }
                return i;
              });
            }
            /**
             * sum: 1,
             * yearMonth: '2025-04'
             */
            if (res.data.data.yearMonthList.length > 0) {
              this.warnInfoMonthCharts = res.data.data.yearMonthList;
            }
            this.initChart();
          }
        })
      });

      Util.request('/hiddenTrouble/getTroubleTypeGroup', {}, 'POST').then(res => {
        if (res.data.code == 'success') {
          let data = [...res.data.data, ...this.warnClassify];
          this.warnClassify = Array.from(new Set(data));
        }
      })
    },
    // 初始化图表
    initChart() {
      this.myCharts && this.myCharts.dispose();
      document.getElementById('monthChart').style.width = document.querySelector('.chart-container').offsetWidth + 'px';
      document.getElementById('monthChart').style.height = document.querySelector('.chart-container').offsetHeight + 'px';
      this.myCharts = echarts.init(document.getElementById('monthChart'));
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '9%',
          right: '4%',
          bottom: '20%',
          top:  '8%'
        },
        xAxis: {
          type: 'category',
          data: this.warnInfoMonthCharts.map(i => i.yearMonth)
        },
        yAxis: {
          type: 'value'
        },
        // 柱状图最大宽度
        barMaxWidth: 40,
        series: [
          {
            name: '隐患数量',
            type: 'bar',
            data: this.warnInfoMonthCharts.map(i => i.sum),
            itemStyle: {
              color: '#2d8cf0'
            }
          }
        ]
      }
      this.myCharts.setOption(option)
    },
    // 获取当前时间
    getCurrentTime() {
      let date = new Date();
      let year = date.getFullYear();
      let month = String(date.getMonth() + 1).padStart(2, '0');
      let day = String(date.getDate()).padStart(2, '0');
      return year + month + day;
    },
    // 导出
    handleExport() {
      if (this.selectedRows.length == 0) {
        this.$Message.warning('请先选择要导出的隐患')
        return;
      }
      let ids = this.selectedRows.map(i => i.id).join(',');
      Util.requestGet('/hiddenTrouble/exportTroubleList/' + ids, {}, 'blob').then(res => {
        // 对于blob响应，直接使用res.data作为文件数据
        let blob = new Blob([res.data], { type: 'application/vnd.ms-excel;charset=utf-8' });
        let url = window.URL.createObjectURL(blob);
        let a = document.createElement('a');
        a.href = url;
        // 获取当前时间 实例 20250709
        a.download = this.title + '隐患排查' + this.getCurrentTime() + '.xlsx';
        a.click();
        window.URL.revokeObjectURL(url);
        this.$Message.success('导出成功');
        this.selectedRows = [];
        this.loadData();
      }).catch(err => {
        console.error('导出失败:', err);
        this.$Message.error('导出失败，请重试');
      })
    },
    // 加载数据
    loadData() {
      this.initRequest()
    },
    // 获取级别颜色
    getLevelColor(level) {
      const colorMap = {
        0: 'green',    // 一般
        1: 'orange',   // 突出
        2: 'red'       // 重大
      }
      return colorMap[level] || 'default'
    },
    // 获取级别文本
    getLevelText(level) {
      const textMap = {
        0: '一般',
        1: '突出',
        2: '重大'
      }
      return textMap[level] || '未知'
    },
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        0: 'red',      // 待整改
        1: 'orange',   // 待复查
        2: 'yellow',   // 待销号
        3: 'green'     // 已销号
      }
      return colorMap[status] || 'default'
    },
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        0: '待整改',
        1: '待复查',
        2: '待销号',
        3: '已销号'
      }
      return textMap[status] || '未知'
    },
    // 处理媒体文件字符串
    getMediaFiles(filesString) {
      if (!filesString) return []
      const files = filesString.split(',').filter(file => file.trim())
      return files.map((file, index) => {
        let option = {};
        if (file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.jpeg')) {
          option.name = '图片' + (index + 1);
          option.url = file.trim();
        }
        if (file.endsWith('.mp4') || file.endsWith('.avi') || file.endsWith('.mov')) {
          option.name = '视频' + (index + 1);
          option.url = file.trim();
        }
        return option;
      });
    },
    // 预览媒体文件
    previewMedia(media) {
      window.open(media.url, '_blank')
    },
    // 格式化时间
    formatDateTime(date, type) {
      if (!date) return ''
      
      let dateObj
      // 处理不同类型的日期输入
      if (typeof date === 'string') {
        dateObj = new Date(date)
      } else if (date instanceof Date) {
        dateObj = date
      } else {
        return ''
      }
      
      // 检查日期是否有效
      if (isNaN(dateObj.getTime())) {
        return ''
      }
      
      const year = dateObj.getFullYear()
      const month = String(dateObj.getMonth() + 1).padStart(2, '0')
      const day = String(dateObj.getDate()).padStart(2, '0')
      
      // 根据类型返回不同的时间格式
      if (type === 'start') {
        // 开始时间：当天 00:00:00
        return `${year}-${month}-${day} 00:00:00`
      } else if (type === 'end') {
        // 结束时间：当天 23:59:59
        return `${year}-${month}-${day} 23:59:59`
      } else {
        // 默认格式：当天 00:00:00
        return `${year}-${month}-${day} 00:00:00`
      }
    },
    // 查询
    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },
    // 重置
    handleReset() {
      this.filterForm = {
        troubleType: '',
        troubleLevel: '',
        troubleStatus: '',
        publishDate: [],
        isOverdue: ''
      }
      this.pagination.current = 1
      this.loadData()
    },
    // 选择行变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    // 分页变化
    handlePageChange(page) {
      this.pagination.current = page
      this.loadData()
    },
    // 页面大小变化
    handlePageSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.pagination.current = 1
      this.loadData()
    },
    // 查看详情
    handleDetail(row) {
      console.log('查看详情:', row)
      Util.request('/hiddenTrouble/getOne/' + row.id).then(res => {
        if (res.data.code == 'success') {
          this.currentHazardData = res.data.data;
          this.detailModalVisible = true;
        }
      })
    },
    // 删除
    handleEdit(row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `<p>确定要删除编号为 <strong>${row.troubleNo}</strong> 的隐患记录吗？</p><p>删除后将无法恢复，请谨慎操作。</p>`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.deleteHazard(row)
        }
      })
    },
    // 删除隐患
    deleteHazard(row) {
      // 模拟删除操作
      Util.request('/hiddenTrouble/deleteTrouble/' + row.id).then(res => {
        if (res.data.code == 'success') {
          this.$Message.success('删除成功')
          this.loadData()
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ivu-card-body {
  width: 100%;
  height: 100%;
}
.header {
  width: 100%;
  height: 24vh;
  margin-bottom: 20px;
  background-color: transparent !important;
}
.hazard-management {
  background-color: #f5f7f9;
  
  .level-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: calc(~"100% - 50px");
    
    .stat-item {
      text-align: center;
      flex: 1;
      
      .count {
        display: block;
        font-size: 24px;
        font-weight: bold;
        line-height: 1;
        
        &.red { color: #ed4014; }
        &.orange { color: #ff9900; }
        &.green { color: #19be6b; }
        &.blue { color: #4A98F7 }
      }
      
      .label {
        display: block;
        margin-top: 4px;
        color: #666;
        font-size: 14px;
      }
      
      .percentage {
        display: block;
        margin-top: 2px;
        color: #999;
        font-size: 12px;
      }
    }
  }
  
  .status-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: calc(~"100% - 50px");
    
    .stat-item {
      text-align: center;
      flex: 1;
      
      .count {
        display: block;
        font-size: 24px;
        font-weight: bold;
        line-height: 1;
        
        &.red { color: #ed4014; }
        &.orange { color: #ff9900; }
        &.yellow { color: #ffcc00; }
        &.green { color: #19be6b; }
      }
      
      .label {
        display: block;
        margin-top: 4px;
        color: #666;
        font-size: 14px;
      }
      
      .percentage {
        display: block;
        margin-top: 2px;
        color: #999;
        font-size: 12px;
      }
    }
  }
  
  .filter-item {
    
    label {
      display: block;
      margin-bottom: 4px;
      color: #666;
      font-size: 14px;
    }
  }
  
  .filter-actions {
    padding-top: 20px;
  }
  
  .chart-container {
    width: 100%;
    height: calc(~'100% - 50px');
  }
}

.month-chart-container {
  /deep/ .ivu-card-body {
    padding: 0;
  }
}
</style> 