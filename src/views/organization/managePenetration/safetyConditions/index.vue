<template>
	<div class="safety-conditions">
		<Row :gutter="16">
			<Col :span="6">
				<ContentCard>
					<div class="scroll-box">
						<div class="actionBtns">
							<Button
								v-auth="'t_safe_table_edit'"
								v-if="!isEditing"
								@click="goEdit(true)"
								type="primary"
								:disabled="
									!this.detailObj.id ||
									!this.detailObj.tableStatus
								"
								>编辑</Button
							>
							<Button
								v-auth="'t_safe_table_edit'"
								v-else
								@click="goEdit(false)"
								type="primary"
								>完成</Button
							>
							<Button type="default" @click="exportFun"
								>导出</Button
							>
						</div>
						<GridTree
							:gridData="gridData"
							@on-select-item="selectOnItem"
							@on-check-item="checkOnItem"
						></GridTree>
					</div>
				</ContentCard>
			</Col>
			<Col :span="18">
				<ContentCard>
					<div
						v-if="detailObj && detailObj.contentName"
						class="scroll-box"
					>
						<div class="title-box">
							{{ detailObj.contentName }}安全条件确认表
						</div>
						<div v-if="detailObj.gridName" class="info-box">
							<div class="info-item">
								网格:{{ detailObj.gridName }}
							</div>
							<div class="info-item">
								施工日期：{{
									$Util.formatDate(
										detailObj.workTime,
										"yyyy-MM-dd"
									)
								}}
							</div>
						</div>
						<div class="table-main">
							<table>
								<tr>
									<th style="width: 100px">检查项目</th>
									<th colspan="2">检查要求</th>
									<th style="width: 120px">检查结果</th>
									<th style="width: 100px">检查人</th>
								</tr>
								<tr
									v-for="(item, index) in safeItemList"
									:key="index"
								>
									<td v-if="item.num" :rowspan="item.num">
										{{ item.checkContent }}
									</td>
									<td>{{ item.checkRequire }}</td>
									<td>{{ item.checkRequireIntro }}</td>
									<td>
										<div
											v-if="!isEditing"
											class="check-box"
										>
											<div
												class="check-box-item"
												:class="{
													active:
														item.subCheckResult ===
														1,
												}"
											>
												<Icon type="ios-checkbox" />
												<div class="rect"></div>
												<div class="name">是</div>
											</div>
											<div
												class="check-box-item"
												:class="{
													active:
														item.subCheckResult ===
														-1,
												}"
											>
												<Icon type="ios-checkbox" />
												<div class="rect"></div>
												<div class="name">否</div>
											</div>
										</div>

										<RadioGroup
											v-else
											v-model="item.subCheckResult"
										>
											<Radio
												:label="1"
												@click.native="
													cancelClick(
														item,
														'subCheckResult',
														1
													)
												"
												>是</Radio
											>
											<Radio
												:label="-1"
												@click.native="
													cancelClick(
														item,
														'subCheckResult',
														-1
													)
												"
												>否</Radio
											>
										</RadioGroup>
									</td>
									<td>{{ item.subCheckUserName }}</td>
								</tr>
								<tr>
									<td>备注</td>
									<td colspan="4">
										<div v-if="!isEditing">
											{{ detailObj.remark }}
										</div>
										<Input
											v-else
											v-model="detailObj.remark"
											type="textarea"
											placeholder="请输入备注"
										></Input>
									</td>
								</tr>
								<tr>
									<td>检查意见</td>
									<td colspan="4">
										<div
											v-if="!isEditing"
											class="check-box"
										>
											<div
												class="check-box-item"
												:class="{
													active:
														detailObj.checkOpinion ===
														1,
												}"
											>
												<Icon type="ios-checkbox" />
												<div class="rect"></div>
												<div class="name">
													符合要求，同意
												</div>
											</div>
											<div
												class="check-box-item"
												:class="{
													active:
														detailObj.checkOpinion ===
														-1,
												}"
											>
												<Icon type="ios-checkbox" />
												<div class="rect"></div>
												<div class="name">
													不同意作业
												</div>
											</div>
										</div>

										<RadioGroup
											v-else
											v-model="detailObj.checkOpinion"
										>
											<Radio
												:label="1"
												@click.native="
													cancelClick(
														detailObj,
														'checkOpinion',
														1
													)
												"
												>符合要求，同意</Radio
											>
											<Radio
												:label="-1"
												@click.native="
													cancelClick(
														detailObj,
														'checkOpinion',
														-1
													)
												"
												>不同意作业</Radio
											>
										</RadioGroup>
									</td>
								</tr>
								<tr>
									<td>人员签字</td>
									<td colspan="4">
										<div
											v-if="signList.length > 0"
											class="img-list"
										>
											<div
												v-for="(
													item, index
												) in signList"
												:key="index"
												@click="checkSignImg(index)"
												class="img-box"
											>
												<img :src="item" />
											</div>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
					<div v-else class="scroll-box">
						<div class="no-data">
							<div>请先选择网格</div>
						</div>
					</div>
				</ContentCard>
			</Col>
		</Row>
		<ImgModal ref="imgModalRef" />
	</div>
</template>
<script>
import GridTree from "./components/gridTree.vue";
import ImgModal from "../classMeeting/components/imgModal.vue";
export default {
	name: "SafetyConditions",
	components: {
		GridTree,
		ImgModal,
	},
	data() {
		return {
			gridData: [],
			curGridObj: {},
			detailObj: {},
			safeItemList: [],
			checkList: [],
			signList: [],
			isEditing: false,
		};
	},
	mounted() {
		this.getTreeData();
	},
	methods: {
		cancelClick(item, key, value) {
			if (item[key] === value) {
				// 再次点击相同值时取消选中
				item[key] = "";
			}
		},

		goEdit(flag) {
			if (flag) {
				this.isEditing = flag;
			} else {
				const putData = this.detailObj;
				delete putData.safeTableRecordVoList;
				putData.safeTableRecordVoList = this.safeItemList;
				console.log(putData, "putData");
				this.goUpdate(putData);
			}
		},

		goUpdate(putData) {
			Util.request("/safeTable/editSafeTable", putData, "put")
				.then((res) => {
					this.$Message.success("编辑成功");
					this.isEditing = false;
				})
				.catch((err) => {
					console.log(err, "err");
				});
		},

		getTreeData() {
			Util.request("/safeTable/getSafeTableTree", {}, "post").then(
				(res) => {
					this.gridData = res.data.data;
				}
			);
		},
		selectOnItem(item) {
			this.curGridObj = item;
			this.signList = [];
			if (item) {
				if (item.dataType === "safeTable") {
					this.getDetailData();
				} else if (item.dataType === "keywork") {
					this.getDetailDataByKeywork();
				} else {
					this.detailObj = {};
				}
			} else {
				this.detailObj = {};
			}
			this.isEditing = false;
		},
		checkOnItem(arr) {
			this.checkList = arr;
			console.log(arr);
		},
		exportFun() {
			let url = "/safeTable/exportSafeTable/";
			if (this.checkList.length > 0) {
				let arr = this.checkList
					.filter(
						(item) => item.checked && item.dataType === "safeTable"
					)
					.map((item) => item.id);
				url += arr.join(",");
			} else {
				url += 0;
			}
			const windowToken = Util.getWindowToken()
			let name = localStorage.getItem(windowToken + "projectName");
			name += "安全确认条件表-";
			name += this.$Util.formatDate(new Date(), "yyyy-MM-dd") + ".zip";
			Util.exportData(url, {}, name, "get");
		},
		getDetailDataByKeywork() {
			this.$Util
				.request(
					"/safeTable/findTableItem",
					{ keyworkId: this.curGridObj.id },
					"post"
				)
				.then((res) => {
					this.detailObj = res.data.data;
					this.safeItemList = this.getSafeItemData(
						this.detailObj.safeTableRecordVoList
					);
					if (this.detailObj.checkUserSign) {
						this.signList = this.detailObj.checkUserSign.split(",");
					}
				});
		},
		getDetailData() {
			this.$Util
				.request(
					"/safeTable/selectTableList",
					{ id: this.curGridObj.id },
					"post"
				)
				.then((res) => {
					this.detailObj = res.data.data[0];
					this.safeItemList = this.getSafeItemData(
						this.detailObj.safeTableRecordVoList
					);
					if (this.detailObj.checkUserSign) {
						this.signList = this.detailObj.checkUserSign.split(",");
					}
				});
		},
		checkSignImg(index) {
			this.$refs.imgModalRef.show(this.signList, index);
		},
		getSafeItemData(list) {
			let arr = [];
			let str = "";
			list.forEach((item) => {
				if (str !== item.checkContent) {
					item.num = 0;
					list.forEach((k) => {
						if (k.checkContent === item.checkContent) {
							item.num++;
						}
					});
					str = item.checkContent;
				}
			});
			console.log(list);
			return list;
		},
	},
};
</script>
<style lang="less" scoped>
@borderColor: #dcdee2;
.safety-conditions {
	.scroll-box {
		position: relative;
		height: calc(~"100vh - 150px");
		overflow: auto;
		.actionBtns {
			position: sticky;
			top: 0;
			margin-bottom: 10px;
		}
	}
	.title-box {
		font-size: 20px;
		font-weight: bold;
		margin-bottom: 10px;
		text-align: center;
		color: #3069ee;
	}
	.no-data {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		font-size: 16px;
		color: #999;
	}
	.check-box {
		display: flex;
		align-items: center;
		column-gap: 8px;
		.check-box-item {
			display: flex;
			align-items: center;
			column-gap: 4px;
			.rect {
				width: 16px;
				height: 16px;
				border: 1px solid @borderColor;
				border-radius: 2px;
			}
			.ivu-icon {
				display: none;
				font-size: 18px;
			}
			&.active {
				color: #3069ee;
				.ivu-icon {
					display: block;
				}
				.rect {
					display: none;
				}
			}
		}
	}
	.info-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 10px;
	}
	.table-main {
		table {
			width: 100%;
			border: 1px solid @borderColor;
			border-collapse: collapse;
			th,
			td {
				border-bottom: 1px solid @borderColor;
				border-right: 1px solid @borderColor;
				padding: 10px;
			}
			th {
			}
		}
		.ivu-checkbox-wrapper:last-child {
			margin-right: 0;
		}
	}
	.img-list {
		display: flex;
		align-items: center;
		gap: 8px;
		.img-box {
			width: 40px;
			height: 40px;
			cursor: pointer;
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
	}
}
</style>
