<template>
	<div class="day-tabs">
		<div class="box-prev" @click="scrollPrev">
			<Icon type="ios-arrow-back" />
		</div>
		<div ref="navScroll" class="day-scrooll">
			<div ref="nav" class="day-list" :style="navStyle">
				<div
					v-for="(date, index) in list"
					:key="index"
					:class="[`status-${date.dateStatus}`, curDay.day === date.day ? 'active' : '']"
					class="box"
					@click="selectDay(date)"
				>
					{{ date.day }}
				</div>
			</div>
		</div>
		<div class="box-next" @click="scrollNext">
			<Icon type="ios-arrow-forward" />
		</div>
	</div>
</template>
<script>
/**
     * 日期状态
     * 0: 无班前会记录
     * 1: 部分网格点有班前会记录
     * 2: 所有网格点都有班前会记录
     * 3: 未到时间（灰色，不可点击）
     */
export default {
	name: "dayTabs",
	props: {
		list: {
			type: Array,
			default: () => [],
		},
		isRefresh: { default: true } // 是否重新赋值当前时间
	},
	data() {
		return {
			navStyle: {
				transform: ''
			},
			curDay: {}
		};
	},
	watch: {
		list: {
			handler(newVal) {
				if (newVal.length && this.isRefresh) {
					this.selectDay(newVal[0])
				}
			},
			deep: true,
			immediate: true
		}
	},
	methods: {
		selectDay(item) {
			if (item.dateStatus === 3) return
			this.curDay = item
			this.$emit('change', item)
		},
		scrollPrev() {
			const containerWidth = this.$refs.navScroll.offsetWidth;
			const currentOffset = this.getCurrentScrollOffset();

			if (!currentOffset) return;

			let newOffset =
				currentOffset > containerWidth
					? currentOffset - containerWidth
					: 0;

			this.setOffset(newOffset);
		},
		scrollNext() {
			const navWidth = this.$refs.nav.offsetWidth;
			const containerWidth = this.$refs.navScroll.offsetWidth;
			const currentOffset = this.getCurrentScrollOffset();
			if (navWidth - currentOffset <= containerWidth) return;

			let newOffset =
				navWidth - currentOffset > containerWidth * 2
					? currentOffset + containerWidth
					: navWidth - containerWidth;

			this.setOffset(newOffset);
		},
		getCurrentScrollOffset() {
			const { navStyle } = this;
			return navStyle.transform
				? Number(
						navStyle.transform.match(
							/translateX\(-(\d+(\.\d+)*)px\)/
						)[1]
				  )
				: 0;
		},
		setOffset(value) {
			this.navStyle.transform = `translateX(-${value}px)`;
		},
	},
};
</script>
<style lang="less" scoped>
@status-color-1: #79a2ff;
@status-color-2: #ffb176;
@status-color-3: #FF3A3A;
.day-tabs {
	position: relative;
	padding: 0 46px;
	color: var(--title-color-1);

}
.day-scrooll {
	background: #f7f8fa;
	height: 48px;
	overflow-x: hidden;
	white-space: nowrap;
	width: 100%;
	padding: 8px 16px 0;
}
.box-next,
.box-prev {
	background: var(--fill-1);
	border-radius: 50%;
	border: none;
	text-align: center;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	width: 30px;
	height: 30px;
	line-height: 30px;
	vertical-align: middle;
	cursor: pointer;
	z-index: 10;
}
.box-prev {
	left: 0;
}
.box-next {
	right: 0;
}
.day-list {
	display: flex;
	width: fit-content;
	.box {
		position: relative;
		width: 24px;
		height: 24px;
		border-radius: 50%;
		border: 1.5px solid transparent;
		margin-right: 18px;
		padding: 0;
		text-align: center;
		line-height: 22px;
		cursor: pointer;
		&.status-3 {
			cursor: not-allowed;
			color: var(--title-color-3);
		}
		&.status-2 {
			border-color: @status-color-1;
			&:hover {
				background: fade(@status-color-1, 50%);
			}
		}
		&.status-1 {
			border-color: @status-color-2;
			&:hover {
				background: fade(@status-color-2, 50%);
			}
		}
		&.status-0 {
			border-color: @status-color-3;
			&:hover {
				background: fade(@status-color-3, 50%);
			}
		}
		// &:hover:not(.status-3) {
		// 	background: rgba(121, 162, 255, 0.5);
		// 	border-color: rgba(121, 162, 255, 0.5);
		// }
		&.active {
			&::after {
				content: "";
				width: 4px;
				height: 4px;
				border-radius: 50%;
				background: var(--primary-color);
				position: absolute;
				bottom: -8px;
				left: 50%;
				transform: translateX(-50%);
			}
		}
	}
}
</style>
