<template>
	<div class="class-item-box" :class="row ? 'row' : 'img'">
		<div class="class-name">{{ label }}:</div>
		<div class="class-info">
			<slot></slot>
		</div>
	</div>
</template>

<script>
export default {
	name: "ClassItemBox",
	props: {
		label: {
			type: String,
			default: "",
		},
		row: { default: true }, // 是否一行显示
	},
};
</script>
<style lang="less" scoped>
.class-item-box {
	display: flex;

	.class-name {
		color: var(--title-color-3);
	}
	.class-info {
		flex: 1;
		color: var(--title-color-1);
	}
	&.row {
		display: flex;
		align-items: center;
		column-gap: 8px;
	}
	&.img {
		display: flex;
		align-items: flex-start;
		column-gap: 8px;
	}
}
</style>
