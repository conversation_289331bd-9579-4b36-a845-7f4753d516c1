<template>
	<div class="class-item">
		<div class="top-bg">
			<img src="@/images/class_box_dec_1.png" alt="" />
		</div>
		<div class="class-item-main">
			<Row :gutter="40" class="mb-32">
				<!-- <Col flex="266px">
					<ClassItemBox label="现场图片">
						<div v-if="imgList.length" class="image-wrapper">
							<img :src="imgList[0]" alt="现场图片" />
							<div class="image-counter">
								{{ imgList.length }}
							</div>
							<div class="check-btn" @click="checkImg(imgList)">
								<Icon type="md-eye" />
							</div>
						</div>
						<div v-else class="no-img">
							<Icon type="md-image" />
							<div class="no-img-text">暂无图片</div>
						</div>
					</ClassItemBox>
				</Col> -->
				<Col flex="1">
					<Row class="mb-16">
						<Col :span="5">
							<ClassItemBox label="班组">
								<div v-if="!isEditing">{{ item.teamName }}</div>
								<Input
									v-else
									v-model="item.teamName"
									style="width: 150px"
									placeholder="请输入班组名称"
									size="small"
									clearable
								></Input>
							</ClassItemBox>
						</Col>
						<Col :span="5">
							<ClassItemBox label="人数">
								<div v-if="!isEditing">
									{{ item.peopleCount }}
								</div>
								<Input
									v-else
									v-model="item.peopleCount"
									style="width: 150px"
									placeholder="请输入人数"
									size="small"
								></Input>
							</ClassItemBox>
						</Col>
						<Col :span="5">
							<ClassItemBox label="上传人">
								<div>{{ item.creatorIdI18n }}</div>
							</ClassItemBox>
						</Col>
						<Col :span="9"
							><ClassItemBox label="班前会时间">
								<div v-if="!isEditing">
									{{ item.meetingTime }}
								</div>

								<DatePicker
									v-else
									v-model="item.meetingTime"
									type="datetime"
									style="width: 200px"
									size="small"
								></DatePicker> </ClassItemBox
						></Col>
					</Row>
					<ClassItemBox label="内容" class="mb-16">
						<div v-if="!isEditing">
							{{ item.content }}
						</div>
						<Input
							v-else
							v-model="item.content"
							type="textarea"
							placeholder="请输入内容"
						></Input>
					</ClassItemBox>
					<Row>
						<Col :span="5">
							<ClassItemBox label="监理" :row="false">
								<div v-if="!isEditing">
									<div
										v-if="item.supervisorPhotoUrl"
										class="avatars"
										@click="
											checkImg([item.supervisorPhotoUrl])
										"
									>
										<img
											:src="item.supervisorPhotoUrl"
											class="avatar"
										/>
									</div>

									<div v-else>未上传</div>
								</div>

								<div
									v-else
									class="avatars"
									@click="
										goDeleteImg(item, 'supervisorPhotoUrl')
									"
								>
									<img
										:src="item.supervisorPhotoUrl"
										class="avatar"
									/>
								</div>
							</ClassItemBox>
						</Col>
						<Col v-if="item.leaderPhotoUrl" :span="5">
							<ClassItemBox label="带班人员" :row="false">
								<div
									class="avatars"
									@click="checkImg([item.leaderPhotoUrl])"
								>
									<img
										:src="item.leaderPhotoUrl"
										class="avatar"
									/>
								</div>
							</ClassItemBox>
						</Col>
						<Col v-if="item.safetyOfficerPhotoUrl" :span="5">
							<ClassItemBox label="网络安全员" :row="false">
								<div
									class="avatars"
									@click="
										checkImg([item.safetyOfficerPhotoUrl])
									"
								>
									<img
										:src="item.safetyOfficerPhotoUrl"
										class="avatar"
									/>
								</div>
							</ClassItemBox>
						</Col>
						<Col v-if="item.guardPhotoUrl" :span="5">
							<ClassItemBox label="防护员" :row="false">
								<div
									class="avatars"
									@click="checkImg([item.guardPhotoUrl])"
								>
									<img
										:src="item.guardPhotoUrl"
										class="avatar"
									/>
								</div>
							</ClassItemBox>
						</Col>
						<Col v-if="item.technicalLeaderPhotoUrl" :span="4">
							<ClassItemBox label="技术负责人" :row="false">
								<div
									class="avatars"
									@click="
										checkImg([item.technicalLeaderPhotoUrl])
									"
								>
									<img
										:src="item.technicalLeaderPhotoUrl"
										class="avatar"
									/>
								</div>
							</ClassItemBox>
						</Col>
					</Row>
				</Col>
			</Row>

			<div class="mb-32">
				<ClassItemBox label="现场图片">
					<div v-if="imgList.length" class="image-wrapper-show">
						<img
							v-for="(item, index) in imgList"
							:key="index"
							:src="item"
							alt="现场图片"
							@click="checkImg(imgList)"
						/>
					</div>
					<div v-else class="no-img">
						<Icon type="md-image" />
						<div class="no-img-text">暂无图片</div>
					</div>
				</ClassItemBox>
			</div>

			<div class="mb-32">
				<ClassItemBox label="上传视频">
					<ShowVideoList v-if="videoList.length" :list="videoList" />
				</ClassItemBox>
			</div>
		</div>
		<ImgModal ref="imgModalRef" />
	</div>
</template>

<script>
import ClassItemBox from "./classItemBox.vue";
import ShowVideoList from "./showVIdeoList.vue";
import ImgModal from "./imgModal.vue";
export default {
	name: "ClassItem",
	components: {
		ClassItemBox,
		ShowVideoList,
		ImgModal,
	},
	props: {
		item: {
			type: Object,
			required: true,
			default() {
				return {};
			},
		},
		isEditing: {
			type: Boolean,
			required: false,
			default: false,
		},
	},
	data() {
		return {
			videoList: [],
			imgList: [],
		};
	},
	watch: {
		item: {
			handler(newVal) {
				this.init();
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		init() {
			let arr = this.item.mediaFilesUrl
				? this.item.mediaFilesUrl.split(",")
				: [];
			// 区分图片和视频
			this.imgList = arr.filter(
				(item) =>
					item.includes(".jpg") ||
					item.includes(".png") ||
					item.includes(".jpeg")
			);
			this.videoList = arr.filter(
				(item) =>
					item.includes(".mp4") ||
					item.includes(".avi") ||
					item.includes(".mov") ||
					item.includes(".wmv") ||
					item.includes(".flv") ||
					item.includes(".mkv")
			);
		},
		checkImg(list) {
			this.$refs.imgModalRef.show(list);
		},

		goDeleteImg(obj, key) {
			obj[key] = "";
		},
	},
};
</script>

<style lang="less" scoped>
.class-item {
	background: #fff;
	border: 1px solid rgba(224, 230, 241, 1);
	border-radius: 8px;
	margin-bottom: 16px;
	img {
		display: block;
	}
	.mb-16 {
		margin-bottom: 16px;
	}
	.mb-32 {
		margin-bottom: 32px;
	}
	.top-bg {
		height: 30px;
		background: linear-gradient(
			90deg,
			rgba(243, 247, 251, 0.7) 0%,
			rgba(195, 212, 255, 0.45) 100%
		);
		border-radius: 8px 8px 0 0;
		position: relative;
		overflow: hidden;
		img {
			display: block;
			height: 100%;
			position: absolute;
			bottom: 0;
			right: 0;
		}
	}
	.class-item-main {
		background: #fff;
		backdrop-filter: blur(4px);
		padding: 16px;
		border-radius: 0 0 8px 8px;
	}
	.no-img {
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 132px;
		justify-content: center;
		gap: 8px;
		color: var(--title-color-3);
		background: var(--fill-1);
		border-radius: 4px;
		.ivu-icon {
			font-size: 48px;
		}
	}
	.image-wrapper {
		position: relative;
		img {
			width: 100%;
			height: 132px;
			object-fit: cover;
			border-radius: 4px;
		}
		.check-btn {
			position: absolute;
			background: rgba(0, 0, 0, 0.24);
			color: #fff;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			font-size: 16px;
			line-height: 22px;
			top: 0;
			left: 0;
			z-index: 5;
		}
		.image-counter {
			position: absolute;
			right: 4.5px;
			top: 4.5px;
			background: rgba(255, 255, 255, 0.6);
			color: var(--title-color-1);
			padding: 0 8px;
			border-radius: 10px;
			font-size: 12px;
			line-height: 17px;
		}
	}

	.image-wrapper-show {
		display: flex;
		align-items: center;
		img {
			width: 60px;
			height: 60px;
			cursor: pointer;
			margin-right: 20px;
		}
	}
	.avatars {
		display: flex;
		gap: 5px;

		.avatar {
			width: 60px;
			height: 60px;
			border-radius: 4px;
			object-fit: cover;
			cursor: pointer;
		}
	}
}
</style>
