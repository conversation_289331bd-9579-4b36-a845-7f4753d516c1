<template>
	<div class="class-meeting">
		<!-- 顶部导航 -->
		<div class="nav-header">
			<div class="top-header">
				<div class="legend-list">
					<div class="box">
						<div class="yuan"></div>
						<div class="name">所有网格点已完成</div>
					</div>
					<div class="box">
						<div class="yuan status-2"></div>
						<div class="name">部分网格点已完成</div>
					</div>
					<div class="box">
						<div class="yuan status-3"></div>
						<div class="name">无网格点完成</div>
					</div>
				</div>
				<div>
					<!-- <Button
						v-auth="'t_before_meet_edit'"
						v-if="!isEditing"
						@click="goEdit(true)"
						type="primary"
						>编辑</Button
					>
					<Button
						v-auth="'t_before_meet_edit'"
						v-else
						@click="goEdit(false)"
						type="primary"
						>完成</Button
					> -->
					<Button @click="exportFile" type="default">导出</Button>
				</div>
			</div>

			<div class="calendar">
				<!-- 日历部分 -->
				<div class="month-box">
					<div class="name">月份</div>
					<DatePicker
						v-model="curMonth"
						type="month"
						placeholder="选择月份"
						style="width: 200px"
						@on-change="changeDate"
						:clearable="false"
					></DatePicker>
				</div>

				<div class="calendar-nav">
					<dayTabs :list="dayList" @change="selectDay"></dayTabs>
				</div>
			</div>
		</div>

		<div class="class-main">
			<div class="gird-list">
				<div
					v-for="item in gridList"
					:key="item.id"
					:class="{ on: curGridObj.id === item.id }"
					class="box"
					@click="selectGrid(item)"
				>
					<Icon type="ios-list-box-outline" />
					<div class="name">{{ item.gridName }}</div>
					<div v-if="!item.hasData" class="has-data">无数据</div>
				</div>
				<div v-if="!gridList.length" class="no-data">
					<img src="@/images/icon_lobar_001.png" alt="" />
					<div class="text">暂无数据</div>
				</div>
			</div>
			<!-- 班前会列表 -->
			<div class="meeting-main">
				<!-- 日期标题 -->
				<div class="date-title">
					<div class="icon-img">
						<img src="@/images/class_icon.png" alt="" />
					</div>
					<div class="date-text">班前会 {{ curDay }}</div>
				</div>
				<div class="class-info">
					<div class="box">
						<div class="name">网格点：</div>
						<div class="val">{{ curGridObj.gridName }}</div>
					</div>
					<div class="box">
						<div class="name">施工日期：</div>
						<div class="val">{{ curDay }}</div>
					</div>
				</div>

				<!-- 班前会内容列表 -->
				<div v-if="meetingList.length" class="meeting-list">
					<ClassItem
						v-for="(item, index) in meetingList"
						:key="index"
						:item="item"
						:isEditing="isEditing"
					/>
				</div>
				<div v-else class="no-data">
					<img src="@/images/icon_lobar_001.png" alt="" />
					<div class="text">暂无数据</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import ClassItem from "./components/classItem.vue";
import dayTabs from "./components/dayTabs";

export default {
	name: "ClassMeeting",
	components: {
		ClassItem,
		dayTabs,
	},
	data() {
		return {
			curMonth: "",
			dayList: [],
			dates: [],
			meetingList: [],
			gridList: [],
			curGridObj: {},
			curDay: "", // 当前选中的日期
			isEditing: false,
		};
	},
	mounted() {
		this.curMonth = this.$Util.formatDate(new Date(), "yyyy-MM");
		this.getDayList();
	},
	methods: {
		goEdit(flag) {
			this.isEditing = flag;
		},

		exportFile() {
			let yearMonth = this.$Util.formatDate(this.curMonth, "yyyy-MM");
			const windowToken = Util.getWindowToken()
			let name = localStorage.getItem(windowToken + "projectName");
			name += this.$Util.formatDate(this.curMonth, "yyyy年MM月");
			name += "班前会台账.xlsx";
			this.$Util.exportData(
				"/railPreMeeting/export?yearMonth=" + yearMonth,
				{},
				name,
				"get"
			);
		},
		selectGrid(item) {
			this.curGridObj = item;
			this.getMeetingData();
		},
		// 选择日期
		selectDay(item) {
			this.curDay = item.date;
			this.getGridData();
		},
		changeDate(date) {
			this.curMonth = this.$Util.formatDate(date, "yyyy-MM");
			this.getDayList();
		},
		getMeetingData() {
			let meetingTimeStart = this.curDay + " 00:00:00";
			let meetingTimeEnd = this.curDay + " 23:59:59";
			let param = {
				page: {
					current: 1,
					size: -1,
				},
				customQueryParams: {
					gridId: this.curGridObj.id,
					meetingTimeStart,
					meetingTimeEnd,
				},
			};
			this.$Util
				.request("/railPreMeeting/list", param, "post")
				.then((res) => {
					this.meetingList = res.data.data.records;
				});
		},
		// 网格点数据
		getGridData() {
			let startTime = this.curDay + " 00:00:00";
			let endTime = this.curDay + " 23:59:59";
			this.$Util
				.request(
					"/railPreMeeting/gridSwitch?startTime=" +
						startTime +
						"&endTime=" +
						endTime
				)
				.then((res) => {
					this.gridList = res.data.data;
					if (this.gridList.length) {
						this.selectGrid(this.gridList[0]);
					}
				});
		},
		// 日期数据
		getDayList() {
			this.$Util
				.request("/railPreMeeting/calendar", {
					yearMonth: this.curMonth,
				})
				.then((res) => {
					this.dayList = res.data.data.map((k, i) => {
						k.day = (i + 1).toString();
						return k;
					});
				});
		},
	},
};
</script>

<style lang="less" scoped>
@status-color-1: #79a2ff;
@status-color-2: #ffb176;
@status-color-3: #ff3a3a;
.class-meeting {
	img {
		display: block;
		width: 100%;
	}
	.nav-header {
		background: #fff;
		padding: 16px;
		margin-bottom: 8px;
		.top-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			.legend-list {
				display: flex;
				align-items: center;
				column-gap: 16px;
				.box {
					display: flex;
					align-items: center;
					color: var(--title-color-2);
					.yuan {
						width: 4px;
						height: 4px;
						background: @status-color-1;
						border-radius: 50%;
						margin-right: 8px;
					}
					.status-2 {
						background: @status-color-2;
					}
					.status-3 {
						background: @status-color-3;
					}
				}
			}
		}

		.calendar {
			display: flex;
			align-items: center;
			.month-box {
				margin-right: 24px;
				display: flex;
				align-items: center;
				.name {
					margin-right: 8px;
					color: var(--title-color-2);
				}
			}

			.calendar-nav {
				flex: 1;
				overflow: hidden;
			}
		}
	}
	.class-main {
		background: #fff;
		padding: 16px;
		display: flex;
		column-gap: 16px;
		.gird-list {
			width: 200px;
			padding: 16px;
			display: flex;
			flex-direction: column;
			flex-wrap: wrap;
			row-gap: 8px;
			box-shadow: 0px 0px 9.9px 0px #5375a72e;
			.box {
				display: flex;
				align-items: center;
				column-gap: 8px;
				padding: 8px 8px 8px 16px;
				border-radius: 4px;
				cursor: pointer;
				color: var(--title-color-2);
				&:hover,
				&.on {
					background: #e8f3ff;
					color: var(--primary-color);
				}
				.name {
					line-height: 20px;
				}
				.has-data {
					background: rgba(255, 124, 124, 0.1);
					line-height: 21px;
					border-radius: 12px;
					color: rgba(245, 63, 63, 1);
					padding: 0 6px;
					font-size: 12px;
					white-space: nowrap;
				}
			}
		}
	}
	.no-data {
		color: var(--title-color-3);
		font-size: 14px;
		line-height: 20px;
		text-align: center;
		padding: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		row-gap: 8px;
		flex-direction: column;
		min-height: 300px;
		img {
			width: 100px;
		}
	}
	.meeting-main {
		flex: 1;
		overflow: hidden;
		.date-title {
			display: flex;
			align-items: center;
			margin-bottom: 8px;
			.icon-img {
				width: 24px;
				height: 24px;
				margin-right: 8px;
			}
			.date-text {
				font-size: 18px;
				color: var(--title-color-1);
				line-height: 24px;
			}
		}
		.class-info {
			display: flex;
			column-gap: 24px;
			align-items: center;
			margin-bottom: 16px;
			.box {
				display: flex;
				align-items: center;
				column-gap: 8px;
				.name {
					color: var(--title-color-3);
				}
				.val {
					color: var(--title-color-1);
				}
			}
		}
	}
	.meeting-list {
	}
}
</style>
