<template>
	<div class="process-monitoring">
		<!-- 顶部导航 -->
		<div class="nav-header">
			<div class="top-header">
				<div class="legend-list">
					<div class="box">
						<div class="yuan"></div>
						<div class="name">所有网格点已完成</div>
					</div>
					<div class="box">
						<div class="yuan status-2"></div>
						<div class="name">部分网格点已完成</div>
					</div>
					<div class="box">
						<div class="yuan status-3"></div>
						<div class="name">无网格点完成</div>
					</div>
				</div>
				<Button @click="exportFile" type="default">导出</Button>
			</div>

			<div class="calendar">
				<!-- 日历部分 -->
				<div class="month-box">
					<div class="name">月份</div>
					<DatePicker
						v-model="curMonth"
						type="month"
						placeholder="选择月份"
						style="width: 200px"
						@on-change="changeDate"
					></DatePicker>
				</div>

				<div class="calendar-nav">
					<dayTabs :list="dayList" @change="selectDay"></dayTabs>
				</div>
			</div>
		</div>

		<div class="process-main">
			<div class="gird-list">
				<div v-for="item in gridList" :key="item.id" :class="{'on': curGridObj.id === item.id}" class="box" @click="selectGrid(item)">
					<Icon type="ios-list-box-outline" />
					<div class="name">{{item.gridName}}</div>
					<div v-if="!item.hasData" class="has-data">无数据</div>
				</div>
				<div v-if="!gridList.length" class="no-data">
					<img src="@/images/icon_lobar_001.png" alt="">
					<div class="text">暂无数据</div>
				</div>
			</div>
			<!-- 过程盯控列表 -->
			<div class="monitoring-main">
				<!-- 日期标题 -->
				<div class="date-title">
					<div class="icon-img">
						<img src="@/images/class_icon.png" alt="" />
					</div>
					<div class="date-text">过程盯控 {{curDay}}</div>
				</div>

				<!-- 过程盯控内容列表 -->
				<div v-if="monitoringList.length" class="monitoring-list">
					<ProcessItem
						v-for="(item, index) in monitoringList"
						:key="index"
						:item="item"
					/>
				</div>
				<div v-else class="no-data">
					<img src="@/images/icon_lobar_001.png" alt="">
					<div class="text">暂无数据</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import ProcessItem from "./components/processItem.vue";
import dayTabs from './components/dayTabs'

export default {
	name: "ProcessMonitoring",
	components: {
		ProcessItem,
		dayTabs
	},
	data() {
		return {
			curMonth: '',
			dayList: [],
			dates: [],
			monitoringList: [],
			gridList: [],
			curGridObj: {},
			curDay: '' // 当前选中的日期
		};
	},
	mounted() {
		this.curMonth = this.$Util.formatDate(new Date(), "yyyy-MM");
		// 先获取日历数据，再根据选中日期获取网格数据
		this.getDayList()
	},
	methods: {
		exportFile() {
			let yearMonth = this.$Util.formatDate(this.curMonth, "yyyy-MM");
			const windowToken = Util.getWindowToken()
			let name = localStorage.getItem(windowToken + 'projectName')
			name += this.$Util.formatDate(this.curMonth, "yyyy年MM月");
			name += '过程盯控台账.xlsx'
			// 过程监控台账导出
			window.open(`${this.$Util.baseURL}/processMonitor/exportList/${yearMonth}`)
		},
		selectGrid(item) {
			this.curGridObj = item
			this.getMonitoringData()
		},
		// 选择日期
		selectDay(item) {
			this.curDay = item.monitorDate
			this.getGridData() // 根据日期获取网格数据
		},
		changeDate(date) {
			this.curMonth = this.$Util.formatDate(date, "yyyy-MM");
			this.getDayList()
		},
		// 获取过程盯控数据
		getMonitoringData() {
			if (!this.curGridObj.id || !this.curDay) {
				this.monitoringList = []
				return
			}
			
			let param = {
				gridId: this.curGridObj.id,
				monitorDate: this.curDay
			}
			
			this.$Util.request('/processMonitor/list', param, 'post').then(res => {
				if (res.data.success) {
					this.monitoringList = res.data.data.map(item => {
						// 处理图片文件，将多个文件字段合并为一个数组
						const mediaFiles = []
						for (let i = 1; i <= 5; i++) {
							const fileKey = `monitorFile${i}`
							if (item[fileKey]) {
								mediaFiles.push(item[fileKey])
							}
						}
						
						return {
							...item,
							// 映射字段以适配组件
							content: item.monitorContent,
							mediaFilesUrl: mediaFiles.join(','),
							// 添加卡片抬头信息
							gridName: item.gridName,
							reporter: item.createUserName,
							constructionDate: item.monitorDate
						}
					})
				} else {
					this.monitoringList = []
					this.$Message.error(res.data.message || '获取数据失败')
				}
			}).catch(err => {
				this.monitoringList = []
				this.$Message.error('获取数据失败')
			})
		},
		// 获取网格点数据（根据选中日期）
		getGridData() {
			if (!this.curDay) {
				this.gridList = []
				return
			}
			
			let startTime = this.curDay + ' 00:00:00'
			let endTime = this.curDay + ' 23:59:59'
			
			this.$Util.request('/railPreMeeting/gridSwitch?startTime=' + startTime + '&endTime=' + endTime).then(res => {
				if (res.data.success) {
					this.gridList = res.data.data || []
					
					// 根据当前选中日期的数据来标记网格是否有数据
					this.markGridDataStatus()
					
					if (this.gridList.length) {
						// 尝试保持当前选中的网格
						let targetGrid = null
						
						// 如果当前有选中的网格，尝试在新的网格列表中找到相同的网格
						if (this.curGridObj && this.curGridObj.id) {
							targetGrid = this.gridList.find(grid => grid.id === this.curGridObj.id)
						}
						
						// 如果没有找到相同的网格，则选择第一个网格
						if (!targetGrid) {
							targetGrid = this.gridList[0]
						}
						
						this.selectGrid(targetGrid)
					} else {
						this.curGridObj = {}
						this.monitoringList = []
					}
				} else {
					this.gridList = []
					this.curGridObj = {}
					this.monitoringList = []
					this.$Message.error(res.data.message || '获取网格数据失败')
				}
			}).catch(err => {
				this.gridList = []
				this.curGridObj = {}
				this.monitoringList = []
				this.$Message.error('获取网格数据失败')
			})
		},
		// 标记网格数据状态
		markGridDataStatus() {
			// 找到当前选中日期的数据
			const currentDayData = this.dayList.find(day => day.monitorDate === this.curDay)
			
			if (currentDayData && currentDayData.gridId) {
				// 将gridId字符串转换为数组
				const hasDataGridIds = currentDayData.gridId.split(',').map(id => parseInt(id.trim()))
				
				// 标记每个网格是否有数据
				this.gridList.forEach(grid => {
					grid.hasData = hasDataGridIds.includes(parseInt(grid.id))
				})
			} else {
				// 如果没有数据，所有网格都标记为无数据
				this.gridList.forEach(grid => {
					grid.hasData = false
				})
			}
		},
		// 获取月份日历数据
		getDayList() {
			// 确保月份格式正确，构造月份第一天的日期
			let monthStr = this.curMonth
			if (typeof this.curMonth === 'object' && this.curMonth instanceof Date) {
				monthStr = this.$Util.formatDate(this.curMonth, 'yyyy-MM')
			}
			const firstDay = monthStr + '-01'
			
			this.$Util.request('/processMonitor/selectListMonth', {
				monitorDate: firstDay
			}, 'post').then(res => {
				if (res.data.success) {
					// 获取当前日期，用于比较
					const today = this.$Util.formatDate(new Date(), 'yyyy-MM-dd')
					
					// 映射数据结构以适配dayTabs组件
					this.dayList = res.data.data.map(item => {
						let dateStatus = item.status
						
						// 如果monitorDate是当前日期之后的时间，则将dateStatus设为3
						if (item.monitorDate > today) {
							dateStatus = 3
						}
						
						return {
							...item,
							dateStatus: dateStatus, // 映射status为dateStatus，未来日期设为3
							date: item.monitorDate    // 保留原始日期字段
						}
					})
					
					// 如果有数据，默认选择第一个有记录的日期，否则选择第一个
					if (this.dayList.length) {
						const firstRecordDay = this.dayList.find(item => item.recordCount > 0) || this.dayList[0]
						this.curDay = firstRecordDay.monitorDate
						this.getGridData() // 根据选中日期获取网格数据
					}
				} else {
					this.dayList = []
					this.$Message.error(res.data.message || '获取日历数据失败')
				}
			}).catch(err => {
				this.dayList = []
				this.$Message.error('获取日历数据失败')
			})
		}
	},
};
</script>

<style lang="less" scoped>
@status-color-1: #79a2ff;
@status-color-2: #ffb176;
@status-color-3: #ff7c7c;
.process-monitoring {
	img {
		display: block;
		width: 100%;
	}
	.nav-header {
		background: #fff;
		padding: 16px;
		margin-bottom: 8px;
		.top-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			.legend-list {
				display: flex;
				align-items: center;
				column-gap: 16px;
				.box {
					display: flex;
					align-items: center;
					color: var(--title-color-2);
					.yuan {
						width: 4px;
						height: 4px;
						background: @status-color-1;
						border-radius: 50%;
						margin-right: 8px;
					}
					.status-2 {
						background: @status-color-2;
					}
					.status-3 {
						background: @status-color-3;
					}
				}
			}
		}

		.calendar {
			display: flex;
			align-items: center;
			.month-box {
				margin-right: 24px;
				display: flex;
				align-items: center;
				.name {
					margin-right: 8px;
					color: var(--title-color-2);
				}
			}

			.calendar-nav {
				flex: 1;
				overflow: hidden;
			}
		}
	}
	.process-main {
		background: #fff;
		padding: 16px;
		display: flex;
		column-gap: 16px;
		.gird-list {
			width: 200px;
			padding: 16px;
			display: flex;
			flex-direction: column;
			flex-wrap: wrap;
			row-gap: 8px;
			box-shadow: 0px 0px 9.9px 0px #5375a72e;
			.box {
				display: flex;
				align-items: center;
				column-gap: 8px;
				padding: 8px 8px 8px 16px;
				border-radius: 4px;
				cursor: pointer;
				color: var(--title-color-2);
				&:hover,
				&.on {
					background: #e8f3ff;
					color: var(--primary-color);
				}
				.name {
					line-height: 20px;
				}
				.has-data {
					background: rgba(255, 124, 124, 0.1);
					line-height: 21px;
					border-radius: 12px;
					color: rgba(245, 63, 63, 1);
					padding: 0 6px;
					font-size: 12px;
					white-space: nowrap;
				}
			}
		}
	}
	.no-data{
		color: var(--title-color-3);
		font-size: 14px;
		line-height: 20px;
		text-align: center;
		padding: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		row-gap: 8px;
		flex-direction: column;
		min-height: 300px;
		img{
			width: 100px;
		}
	}
	.monitoring-main {
		flex: 1;
		overflow: hidden;
		.date-title {
			display: flex;
			align-items: center;
			margin-bottom: 8px;
			.icon-img {
				width: 24px;
				height: 24px;
				margin-right: 8px;
			}
			.date-text {
				font-size: 18px;
				color: var(--title-color-1);
				line-height: 24px;
			}
		}
	}
	.monitoring-list {
	}
}
</style>

