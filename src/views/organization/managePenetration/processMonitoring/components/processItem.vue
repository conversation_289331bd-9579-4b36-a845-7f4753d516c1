<template>
	<div class="process-item">
		<div class="top-bg">
			<div class="header-info">
				<div class="info-item">
					<span class="label">网格点：</span>
					<span class="value">{{ item.gridName || '-' }}</span>
				</div>
				<div class="info-item">
					<span class="label">填报人：</span>
					<span class="value">{{ item.reporter || '-' }}</span>
				</div>
				<div class="info-item">
					<span class="label">施工日期：</span>
					<span class="value">{{ item.constructionDate || '-' }}</span>
				</div>
			</div>
			<img src="@/images/class_box_dec_1.png" alt="" />
		</div>
		<div class="process-item-main">
			<!-- 盯控内容部分 -->
			<ProcessItemBox label="盯控内容" class="content-section">
				<div class="content-text">{{ item.content || '暂无内容' }}</div>
			</ProcessItemBox>
			
			<!-- 图片/影像部分 -->
			<ProcessItemBox label="图片/影像" class="media-section">
				<!-- 图片展示 -->
				<div v-if="imgList.length" class="image-gallery">
					<div 
						v-for="(img, index) in imgList" 
						:key="index"
						class="image-item"
						@click="checkImg(imgList, index)"
					>
						<img :src="img" alt="现场图片" />
					</div>
				</div>
				
				<!-- 视频展示 -->
				<div v-if="videoList.length" class="video-section">
					<ShowVideoList :list="videoList" />
				</div>
				
				<!-- 无媒体文件时显示 -->
				<div v-if="!imgList.length && !videoList.length" class="no-media">
					<Icon type="md-image" />
					<div class="no-media-text">暂无图片/视频</div>
				</div>
			</ProcessItemBox>
		</div>
		<ImgModal ref="imgModalRef" />
	</div>
</template>

<script>
import ProcessItemBox from "./processItemBox.vue";
import ShowVideoList from "../../classMeeting/components/showVIdeoList.vue";
import ImgModal from "./imgModal.vue";

export default {
	name: "ProcessItem",
	components: {
		ProcessItemBox,
		ShowVideoList,
		ImgModal
	},
	props: {
		item: {
			type: Object,
			required: true,
			default() { return {} }
		},
	},
	data() {
		return {
			imgList: [],
			videoList: []
		}
	},
	watch: {
		item: {
			handler(newVal) {
				this.init()
			},
			deep: true,
			immediate: true
		}
	},
	methods: {
		init() {
			let arr = this.item.mediaFilesUrl ? this.item.mediaFilesUrl.split(',') : []
			// 区分图片和视频
			this.imgList = arr.filter(item => 
				item.includes('.jpg') || 
				item.includes('.png') || 
				item.includes('.jpeg') ||
				item.includes('.gif') ||
				item.includes('.bmp') ||
				item.includes('.webp')
			)
			this.videoList = arr.filter(item => 
				item.includes('.mp4') || 
				item.includes('.avi') || 
				item.includes('.mov') || 
				item.includes('.wmv') || 
				item.includes('.flv') || 
				item.includes('.mkv')
			)
		},
		checkImg(list, index = 0) {
			this.$refs.imgModalRef.show(list, index)
		}
	}
};
</script>

<style lang="less" scoped>
.process-item {
	background: #fff;
	border: 1px solid rgba(224, 230, 241, 1);
	border-radius: 8px;
	margin-bottom: 16px;
	img{
		display: block;
	}
	.top-bg {
		height: 50px;
		background: linear-gradient(
			90deg,
			rgba(243, 247, 251, 0.7) 0%,
			rgba(195, 212, 255, 0.45) 100%
		);
		border-radius: 8px 8px 0 0;
		position: relative;
		overflow: hidden;
		padding: 12px 16px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		.header-info {
			display: flex;
			align-items: center;
			gap: 24px;
			z-index: 1;
			
			.info-item {
				display: flex;
				align-items: center;
				font-size: 12px;
				
				.label {
					color: var(--title-color-3);
					margin-right: 4px;
				}
				
				.value {
					color: var(--title-color-1);
					font-weight: 500;
				}
			}
		}
		
		img {
			display: block;
			height: 100%;
			position: absolute;
			bottom: 0;
			right: 0;
			z-index: 0;
		}
	}
	.process-item-main {
		background: #fff;
		backdrop-filter: blur(4px);
		padding: 20px;
		border-radius: 0 0 8px 8px;
		
		.content-section {
			margin-bottom: 24px;
			
			.label {
				font-weight: 500;
				margin-bottom: 8px;
			}
		}
		
		.media-section {
			margin-bottom: 0;
			
			.label {
				font-weight: 500;
				margin-bottom: 12px;
			}
		}
	}
	
	.content-text {
		min-height: 80px;
		line-height: 1.6;
		word-break: break-all;
		font-size: 14px;
		color: var(--title-color-1);
		white-space: pre-wrap;
		background: #fafbfc;
		padding: 12px;
		border-radius: 6px;
		border: 1px solid #e8ebf0;
	}
	
	.image-gallery {
		margin-bottom: 16px;
		display: flex;
		flex-wrap: wrap;
		gap: 12px;
		
		.image-item {
			width: 160px;
			height: 120px;
			border-radius: 6px;
			overflow: hidden;
			cursor: pointer;
			transition: all 0.2s ease;
			border: 1px solid #e8ebf0;
			box-shadow: 0 2px 4px rgba(0,0,0,0.05);
			
			&:hover {
				transform: scale(1.05);
				box-shadow: 0 6px 16px rgba(0,0,0,0.12);
				border-color: var(--primary-color);
			}
			
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
	}
	
	.video-section {
		margin-top: 16px;
	}
	
	.no-media{
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 120px;
		justify-content: center;
		gap: 8px;
		color: var(--title-color-3);
		background: #fafbfc;
		border-radius: 6px;
		border: 1px dashed #d0d7de;
		
		.ivu-icon{
			font-size: 48px;
		}
		
		.no-media-text {
			font-size: 12px;
		}
	}
}
</style> 