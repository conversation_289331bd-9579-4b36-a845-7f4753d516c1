<template>
	<div class="class-item">
		<div class="top-bg">
			<img src="@/images/class_box_dec_1.png" alt="" />
		</div>
		<div class="class-item-main">
			<Row class="mb-16">
				<Col :span="8">
					<ClassItemBox label="主讲人">{{
						item.speaker
					}}</ClassItemBox>
				</Col>
				<Col :span="8">
					<ClassItemBox label="参加人数"
						>{{ item.attendeeCount }}人</ClassItemBox
					>
				</Col>
				<Col :span="8"
					><ClassItemBox label="地点">{{
						item.location
					}}</ClassItemBox></Col
				>
			</Row>
			<ClassItemBox label="当日工作完成情况" :row="false" class="mb-16">{{
				item.workCompletion
			}}</ClassItemBox>
			<ClassItemBox label="存在问题及需要协调解决的事项" :row="false" class="mb-16">{{
				item.issues
			}}</ClassItemBox>
			<ClassItemBox label="次日作业安全质量风险研判及管控措施" :row="false" class="mb-16">{{
				item.riskJudgment
			}}</ClassItemBox>
			<ClassItemBox label="次日工作安排" :row="false" class="mb-16">{{
				item.nextDayPlan
			}}</ClassItemBox>
			<ClassItemBox label="签名" class="mb-16">
				<div class="img-list" v-if="signList.length">
					<div v-for="(item, index) in signList" :key="index" @click="checkSignImg(index)" class="img-item">
						<img  :src="item" alt="签名" />
					</div>
				</div>
			</ClassItemBox>
			<ClassItemBox label="图片/影像">
				<div class="img-list mb-16" v-if="imgList.length">
					<div v-for="(item, index) in imgList" :key="index" @click="checkImg(index)" class="img-item">
						<img :src="item" alt="现场图片" />
					</div>
				</div>
				<ShowVideoList v-if="videoList" :list="videoList" />
			</ClassItemBox>
		</div>
		<ImgModal ref="imgModalRef" />
	</div>
</template>

<script>
import ClassItemBox from "../../classMeeting/components/classItemBox.vue";
import ShowVideoList from "../../classMeeting/components/showVIdeoList.vue";
import ImgModal from "../../classMeeting/components/imgModal.vue";
export default {
	name: "ClassItem",
	components: {
		ClassItemBox,
		ShowVideoList,
		ImgModal,
	},
	props: {
		item: {
			type: Object,
			required: true,
			default() {
				return {};
			},
		},
	},
	data() {
		return {
			videoList: [],
			imgList: [],
			signList: []
		};
	},
	watch: {
		item: {
			handler(newVal) {
				this.init();
			},
			deep: true,
			immediate: true
		},
	},
	methods: {
		init() {
			let arr = this.item.mediaFilesUrl
				? this.item.mediaFilesUrl.split(",")
				: [];
			// 区分图片和视频
			this.imgList = arr.filter(
				(item) =>
					item.includes(".jpg") ||
					item.includes(".png") ||
					item.includes(".jpeg")
			);
			this.videoList = arr.filter(
				(item) =>
					item.includes(".mp4") ||
					item.includes(".avi") ||
					item.includes(".mov") ||
					item.includes(".wmv") ||
					item.includes(".flv") ||
					item.includes(".mkv")
			);
			this.signList = this.item.signUsers.filter(k => k.signatureImageUrl).map(k => k.signatureImageUrl)
		},
		checkImg(index) {
			this.$refs.imgModalRef.show(this.imgList, index);
		},
		checkSignImg(index) {
			this.$refs.imgModalRef.show(this.signList, index);
		}
	},
};
</script>

<style lang="less" scoped>
.class-item {
	background: #fff;
	border: 1px solid rgba(224, 230, 241, 1);
	border-radius: 8px;
	margin-bottom: 16px;
	img {
		display: block;
	}
	.mb-16 {
		margin-bottom: 16px;
	}
	.mb-32 {
		margin-bottom: 32px;
	}
	.top-bg {
		height: 30px;
		background: linear-gradient(
			90deg,
			rgba(243, 247, 251, 0.7) 0%,
			rgba(195, 212, 255, 0.45) 100%
		);
		border-radius: 8px 8px 0 0;
		position: relative;
		overflow: hidden;
		img {
			display: block;
			height: 100%;
			position: absolute;
			bottom: 0;
			right: 0;
		}
	}
	.class-item-main {
		background: #fff;
		backdrop-filter: blur(4px);
		padding: 16px;
		border-radius: 0 0 8px 8px;
	}
	.ClassItemBox.label {
		display: flex;
		align-items: center;
	}
	.img-list{
		display: flex;
		column-gap: 8px;
		row-gap: 8px;
		flex-wrap: wrap;
		align-items: center; // 让图片整体与标签垂直居中
		.img-item{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 110px;
			height: 60px;
			box-shadow: none; // 去掉阴影
			img{
				width: 100%;
				height: 100%;
				border-radius: 4px;
				object-fit: contain;
				cursor: pointer;
				background: #f5f5f5;
				box-shadow: none; // 去掉图片阴影
			}
		}
	}
}
</style>
