.record() {
  .tableBox {
    width: 100%;

    .title {
      font-size: 22px;
      font-weight: 600;
      text-align: center;
    }

    .table {
      position: relative;
      width: 75%;
      text-align: center;
      margin: auto;
      margin-bottom: 50px;

      &:not(:last-child) {
        border-bottom: 2px solid #ececec;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 50px;

        tr,
        td {
          border: 1px solid #000;
        }

        td {
          text-align: center;
          padding: 5px;

          &.textLeft {
            text-align: left;
          }

          &.catalogContent {
            writing-mode: vertical-rl;
          }

          .signBox {
            display: flex;
            align-items: center;
            justify-content: space-evenly;

            .signItem {
              display: flex;
              align-items: center;

              .signImg {
                width: 100px;

              }
            }
          }


        }
      }

      .breaker {
        margin: 0 20px;
      }

      .projectName {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-bottom: 5px;
      }
    }
  }

  .FlexBetween {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}