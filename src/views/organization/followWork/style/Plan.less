.Plan() {
  .titleBtn {}

  .workList {
    background-color: #f5f8fb;
    padding: 16px;
    margin: 16px 0;

    .workTitle {
      font-size: 18px;
      text-align: left;
      margin-bottom: 8px;
    }

    .workItem {
      &:not(:last-child) {
        margin-bottom: 10px;
      }
    }
  }

  .date-box {
    display: flex;
    justify-content: space-between;
    height: 100%;

    /deep/ .ivu-col {
      height: 100%;
    }

    .date-list {
      width: 13%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      row-gap: 8px;
      box-shadow: 0px 0px 9.9px 0px #5375a72e;

      .projectName {
        font-size: 16px;
      }

      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        
        img {
          width: 64px;
          height: 64px;
          margin-bottom: 16px;
        }
        
        .text {
          color: #999;
          font-size: 14px;
        }
      }

      .box {
        display: flex;
        align-items: center;
        column-gap: 8px;
        padding: 8px 8px 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        color: var(--title-color-2);

        &:hover,
        &.on {
          background: #e8f3ff;
          color: var(--primary-color);
        }

        .name {
          line-height: 20px;
        }

        .has-data {
          background: rgba(255, 124, 124, 0.1);
          line-height: 21px;
          border-radius: 12px;
          color: rgba(245, 63, 63, 1);
          padding: 0 6px;
          font-size: 12px;
          white-space: nowrap;
        }
      }
    }
  }

  .no-data {
    width: 85%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    
    img {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
    }
    
    .text {
      color: #999;
      font-size: 16px;
    }
  }

  .date-content {
    width: 85%;

    .date-title {
      position: sticky;
      top: 1px;
      display: flex;
      align-items: center;
      position: relative;
      font-size: 18px;
      text-align: left;
      margin-bottom: 20px;

      img {
        width: 24px;
        height: 24px;
      }

      .export-btn {
        position: absolute;
        right: 90px;
        /* 为保存按钮留出空间 */
        display: inline-block;
        cursor: pointer;
      }

      .save-btn {
        position: absolute;
        right: 0;
        display: inline-block;
        cursor: pointer;
      }
    }



    .date-desc {
      .desc {
        display: flex;
        justify-content: space-between;

        .left {
          display: flex;
          justify-content: space-between;

          .count {
            margin-right: 10px;

            .heightLight {
              color: #067cfe;
            }
          }
        }

        .right {
          font-size: 14px;
        }
      }
    }

    .date-table {
      max-height: 600px;
      overflow-y: auto;
      border: 1px solid #E0E6F1;
      border-radius: 4px;

      table {
        width: 100%;
        border-collapse: collapse;

        td,
        tr {
          text-align: center;
        }

        .head-tr {
          position: sticky;
          top: 0;
          z-index: 9999;

          th {
            width: 40px !important;
            text-align: center;
            padding: 20px 0;
            background-color: #f3f7fb;
            border-bottom: 2px solid #E0E6F1;
            border-left: 1px solid #E0E6F1;
            border-right: 1px solid #E0E6F1;

            &:first-child {
              border-left: none;
            }

            &:last-child {
              border-right: none;
            }
          }

          .day {
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            font-weight: 600;
            margin: auto;
          }
        }

        .body-tr {
          background-color: #ddedf4;

          td {
            padding: 20px 0;
            border-left: 1px solid #E0E6F1;
            border-right: 1px solid #E0E6F1;
            border-bottom: 1px solid #E0E6F1;
            cursor: pointer;

            &:first-child {
              border-left: none;
            }

            &:last-child {
              border-right: none;
            }

            &.clo_one {
              background-color: #ffffff;
            }

            &.clo_two {
              background-color: #f8fafd;
            }

            &:hover {
              .symbol {
                opacity: 0.3;
              }

              .place {
                opacity: 0.5;
              }
            }
          }

          .symbol {
            position: relative;
            opacity: 1;

            img {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 20px;
              height: 20px;
            }
          }

          .place {
            position: relative;
            opacity: 0;

            img {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 20px;
              height: 20px;
            }
          }

        }
      }
    }
  }
}