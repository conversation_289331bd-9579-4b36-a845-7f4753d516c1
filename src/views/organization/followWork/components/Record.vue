<template>
	<div>
		<div class="nav-header">
			<div class="top-header">
				<div class="legend-list">
					<div class="box">
						<div class="yuan status-1"></div>
						<div class="name">已填写</div>
					</div>

					<div class="box">
						<div class="yuan status-2"></div>
						<div class="name">未填写</div>
					</div>
				</div>

				<div>
					<Button @click="exportFile" type="default">导出</Button>
				</div>
			</div>

			<div class="calendar">
				<!-- 日历部分 -->
				<div class="month-box">
					<div class="name">月份</div>
					<DatePicker
						v-model="curMonth"
						type="month"
						placeholder="选择月份"
						style="width: 200px"
						@on-change="changeDate"
					></DatePicker>
				</div>

				<div class="calendar-nav">
					<dayTabs :list="dayList" @change="selectDay"></dayTabs>
				</div>
			</div>
		</div>

		<div v-if="tableList.length" class="tableBox">
			<div class="title">项目领导跟班记录表</div>

			<div v-for="(item, index) in tableList" :key="index" class="table">
				<div class="projectName">
					<div>工程名称：{{ item.projectName || "" }}</div>

					<div>
						<Button
							v-auth="'t_follow_work_edit'"
							v-if="isEditing === index"
							@click="goEdit(false)"
							type="primary"
							size="small"
							>完成</Button
						>

						<Button
							v-auth="'t_follow_work_edit'"
							v-else
							@click="goEdit(index)"
							:disabled="!item.status"
							type="primary"
							size="small"
							>编辑</Button
						>
					</div>
				</div>
				<table>
					<tr>
						<td>施工单位</td>
						<td colspan="2">
							<div class="FlexBetween">
								<div>{{ item.constructionUnit || "" }}</div>
								<div>
									检查日期：{{
										getTime(item.checkDate) || ""
									}}
								</div>
							</div>
						</td>
					</tr>

					<tr>
						<td>检查依据</td>
						<td colspan="2" class="textLeft">
							{{ item.checkBasis || "" }}
						</td>
					</tr>

					<tr>
						<td>检查项目</td>
						<td>检查内容</td>
						<td>检查结论</td>
					</tr>

					<tbody v-for="(val, ind) in item.checkResults" :key="ind">
						<tr v-for="(val_I, ind_I) in val.children" :key="ind_I">
							<td
								v-if="!ind_I"
								class="catalogContent"
								:rowspan="val.children.length"
							>
								{{ val.catalogContent || "" }}
							</td>
							<td class="textLeft">
								{{ val_I.catalogContent || "" }}
							</td>
							<td>
								<RadioGroup
									v-if="isEditing === index"
									v-model="val_I.checkConclusion"
								>
									<Radio
										:label="1"
										@click.native="
											cancelClick(
												val_I,
												'checkConclusion',
												1
											)
										"
										>是</Radio
									>
									<Radio
										:label="2"
										@click.native="
											cancelClick(
												val_I,
												'checkConclusion',
												2
											)
										"
										>否</Radio
									>
									<Radio
										:label="3"
										@click.native="
											cancelClick(
												val_I,
												'checkConclusion',
												3
											)
										"
										>不涉及</Radio
									>
								</RadioGroup>

								<div v-else>
									{{ getResult(val_I.checkConclusion) }}
								</div>
							</td>
						</tr>
					</tbody>

					<tr>
						<td>当日值班情况记要</td>
						<td colspan="2" class="textLeft">
							<Input
								v-if="isEditing === index"
								v-model="item.dutySituationSummary"
								type="textarea"
								placeholder="请输入当日值班情况记要"
							></Input>

							<div v-else>
								{{ item.dutySituationSummary || "" }}
							</div>
						</td>
					</tr>

					<tr>
						<td>带班生产结果</td>
						<td colspan="2">
							<RadioGroup
								v-if="isEditing === index"
								v-model="item.productionResult"
							>
								<Radio
									v-for="(val, ind) in productList"
									:key="ind"
									:label="val.itemValue"
									@click.native="
										cancelClick(
											item,
											'productionResult',
											val.itemValue
										)
									"
									>{{ val.itemText }}</Radio
								>
							</RadioGroup>

							<div v-else>
								{{ getProductRes(item.productionResult) }}
							</div>
						</td>
					</tr>

					<tr>
						<td>备注</td>
						<td colspan="2" class="textLeft">
							<Input
								v-if="isEditing === index"
								v-model="item.remark"
								type="textarea"
								placeholder="请输入备注"
							></Input>

							<div v-else>
								{{ item.remark || "" }}
							</div>
						</td>
					</tr>

					<tr>
						<td colspan="3">
							<div class="signBox">
								<span class="signItem">
									<span>跟班领导：</span>
									<img
										v-if="item.fillUserSignUrl"
										class="signImg"
										:src="item.fillUserSignUrl"
									/>
								</span>

								<span class="breaker" />

								<span class="signItem">
									<span>接班领导：</span>
									<img
										v-if="item.handoverUserSignUrl"
										class="signImg"
										:src="item.handoverUserSignUrl"
									/>
								</span>
							</div>
						</td>
					</tr>
				</table>
			</div>
		</div>

		<div v-else class="tableBox">
			<div class="title">该日期暂无记录</div>
		</div>
	</div>
</template>

<script>
import Util from "@/libs/util";
import DayTabs from "../../managePenetration/classMeeting/components/dayTabs.vue";
import moment from "moment";
const windowToken = Util.getWindowToken();
export default {
	components: {
		DayTabs,
	},
	data() {
		return {
			curMonth: moment(new Date()).format("YYYY-MM"),
			dayList: [],
			tableList: [],
			productList: [],
			projectName: localStorage.getItem(windowToken + "projectName") || "项目",
			isEditing: "",
		};
	},
	mounted() {
		this.getDayList();
		this.getProductList();
	},
	methods: {
		goEdit(index) {
			if (index || index === 0) {
				this.isEditing = index;
			} else {
				const putData = this.tableList[this.isEditing];

				console.log(putData, "putData");
				this.goUpdate(putData);
			}
		},

		goUpdate(putData) {
			Util.request("/railFollowRecord", putData, "put")
				.then((res) => {
					this.$Message.success("编辑成功");
					this.isEditing = "";
				})
				.catch((err) => {
					console.log(err, "err");
				});
		},

		cancelClick(item, key, value) {
			if (item[key] === value) {
				// 再次点击相同值时取消选中
				item[key] = "";
			}
		},

		changeDate(date) {
			this.curMonth = moment(this.curMonth).format("YYYY-MM");
			this.getDayList();
		},

		getTime(time) {
			const year = moment(time).format("YYYY");
			const month = moment(time).format("MM");
			const day = moment(time).format("DD");

			return `${year} 年 ${month} 月 ${day} 日`;
		},

		getDayList() {
			this.$Util
				.request("/railFollowRecord/getRecordCalendar", {
					yearMonth: this.curMonth,
				})
				.then((res) => {
					this.dayList = res.data.data.map((item, index) => {
						item.day = (index + 1).toString();
						// 交换 dateStatus 1 和 2 的值
						if (item.dateStatus === 1) {
							item.dateStatus = 2;
						} else if (item.dateStatus === 2) {
							item.dateStatus = 1;
						}
						return item;
					});
				});
		},

		getRecord(month) {
			this.$Util
				.request("/railFollowRecord/getRecordsByDay", {
					date: month,
				})
				.then((res) => {
					console.log(res, "res");
					this.tableList = res.data.data;
				});
		},

		selectDay(e) {
			this.getRecord(e.date);
		},

		exportFile() {
			const url = `/railFollowRecord/exportByMonth?yearMonth=${this.curMonth}`;
			Util.request(url, {}, "get", {}, "blob")
				.then((resp) => {
					var blob = new Blob([resp.data], {
						type: "application/octet-stream",
					});
					var downloadElement = document.createElement("a");
					var href = window.URL.createObjectURL(blob);
					downloadElement.href = href;

					// 格式化年月信息
					const year = this.curMonth.split("-")[0];
					const month = this.curMonth.split("-")[1];
					const formattedMonth = `${year}年${month}月`;

					// 获取当前日期作为导出时间
					const exportDate = moment().format("YYYYMMDD");

					// 组合文件名：项目名称 + 年月 + 跟班记录 + 导出时间
					const fileName = `${this.projectName}${formattedMonth}跟班记录${exportDate}.xls`;

					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				})
				.catch(function (error) {
					console.log("导出发生错误！", error);
				});
		},

		getResult(key) {
			switch (key) {
				case 1:
					return "☑是 ☐否 ☐不涉及";
				case 2:
					return "☐是 ☑否 ☐不涉及";
				case 3:
					return "☐是 ☐否 ☑不涉及";
				default:
					return "☐是 ☐否 ☐不涉及";
			}
		},

		getProductRes(key) {
			let str = "";
			this.productList.forEach((item) => {
				let displayText = item.itemText;

				if (item.itemValue === key) {
					str += `☑${displayText}  `;
				} else {
					str += `☐${displayText}  `;
				}
			});
			return str;
		},

		getProductList() {
			const url = `/sys/dict/getDicItem/follow_production_result`;
			this.$Util.request(url, {}).then((res) => {
				const arr = res.data.data;
				arr.forEach((item) => {
					item.itemValue = 1 * item.itemValue;
					// 如果是"其他"选项，添加特殊说明
					if (item.itemText === "其他") {
						item.itemText = "其他（在值班纪要填写）";
					}
				});
				this.productList = arr;
				console.log(this.productList, "this.productList");
			});
		},
	},
};
</script>

<style lang="less" scoped>
@import "../style/Record.less";

@status-color-1: #79a2ff;
@status-color-2: #ffb176;
@status-color-3: #ff7c7c;

.nav-header {
	background: #fff;
	padding: 16px;
	margin-bottom: 8px;
	.top-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;
		.legend-list {
			display: flex;
			align-items: center;
			column-gap: 16px;
			.box {
				display: flex;
				align-items: center;
				color: var(--title-color-2);
				.yuan {
					width: 4px;
					height: 4px;
					background: @status-color-1;
					border-radius: 50%;
					margin-right: 8px;
				}
				.status-2 {
					background: @status-color-2;
				}
				.status-3 {
					background: @status-color-3;
				}
			}
		}
	}

	.calendar {
		display: flex;
		align-items: center;
		.month-box {
			margin-right: 24px;
			display: flex;
			align-items: center;
			.name {
				margin-right: 8px;
				color: var(--title-color-2);
			}
		}

		.calendar-nav {
			flex: 1;
			overflow: hidden;
		}
	}
}

.record();
</style>
