<template>
	<div class="plan">
		<div class="titleBtn">
			<Button type="default" @click="modalOpen">跟班人员设置</Button>
		</div>

		<div class="workList">
			<div class="workTitle">跟班作业要求</div>
			<div
				v-for="(item, index) in workList"
				class="workItem"
				:key="index"
			>
				<span class="num">{{ index + 1 }}.</span>
				<span class="text">{{ item }}</span>
			</div>
		</div>

		<div class="date-box">
			<div class="date-list">
				<div class="projectName">{{ projectName }}</div>
				<div
					v-for="item in dateList"
					:key="item.yearMonth"
					:class="{ on: currentMonth === item.yearMonth }"
					class="box"
					@click="checkDate(item.yearMonth)"
				>
					<Icon type="ios-list-box-outline" />
					<div class="name">{{ item.displayName }}</div>
				</div>
				<div v-if="!dateList.length" class="no-data">
					<img src="@/images/icon_lobar_001.png" alt="" />
					<div class="text">暂无数据</div>
				</div>
			</div>

			<div class="date-content" v-if="dateList && dateList.length > 0">
				<div class="date-title">
					<img src="@/images/class_icon.png" alt="" />
					<span>{{ getDate(yearMonth) }}</span>
					<span>跟班计划</span>

					<div class="export-btn" @click="exportPlan">
						<Button type="default">导出</Button>
					</div>
					<div class="save-btn" @click="savePlan" v-if="canEdit">
						<Button type="primary">保存</Button>
					</div>
				</div>

				<div class="date-desc">
					<div class="desc">
						<div class="left">
							<span>计划跟班总次数：</span>
							<span
								v-for="(item, index) in userStats"
								:key="index"
								class="count"
							>
								<span>{{ item.userIdI18n }}: </span>
								<span class="heightLight"
									>{{ item.planCount }}次</span
								>
							</span>
						</div>
						<div class="right">
							统计说明：同一人一天去多个网格算一次
						</div>
					</div>
				</div>

				<div class="date-table">
					<table ref="gridTable">
						<tr class="head-tr">
							<th>网格点</th>
							<th>跟班人员</th>
							<th v-for="day in totalDays" :key="day">
								<div class="day">{{ getDay(day) }}</div>
							</th>
						</tr>

						<tbody v-for="(item, index) in planArr" :key="index">
							<tr
								v-for="(val, ind) in item.arr"
								:key="val.name + ind"
								class="body-tr"
							>
								<td
									v-if="!ind"
									class="gridName"
									:class="
										index % 2 === 0 ? 'clo_one' : 'clo_two'
									"
									:rowspan="item.arr.length"
								>
									{{ item.gridName }}
								</td>
								<td
									:class="
										index % 2 === 0 ? 'clo_one' : 'clo_two'
									"
								>
									{{ val.name }}
								</td>
								<td
									v-for="day in totalDays"
									:class="
										index % 2 === 0 ? 'clo_one' : 'clo_two'
									"
									:key="day"
									@click="tooglePlan(val, day)"
								>
									<div
										v-if="val.plan.includes(day)"
										class="symbol"
									>
										<img src="../icons/correct.svg" />
									</div>
									<div v-else class="place">
										<img src="../icons/correct.svg" />
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			
			<div class="no-data" v-else>
				<img src="@/images/icon_lobar_001.png" alt="" />
				<div class="text">暂无跟班计划</div>
			</div>
		</div>

		<Modal v-model="modalShow" :title="modalTitle">
			<div>
				<div style="margin-bottom: 5px">选择跟班人员</div>
				<div
					style="margin-bottom: 5px"
					v-for="(item, index) in checkList"
					:key="index"
				>
					<Checkbox v-model="item.value">{{
						item.realName
					}}</Checkbox>
				</div>
			</div>
			<div slot="footer">
				<Button @click.native="modalCancel">关闭</Button>
				<Button type="primary" @click.native="modalConfirm"
					>确定</Button
				>
			</div>
		</Modal>
	</div>
</template>

<script>
import Util from "@/libs/util";
import moment from "moment";
const windowToken= Util.getWindowToken();
export default {
	data() {
		return {
			projectName: localStorage.getItem(windowToken+"projectName"),
			modalShow: false,
			modalTitle: "跟班人员设置",
			checkList: [],
			workList: [],
			currentId: "",
			dateList: [],
			planArr: [],
			originalPlanArr: [], // 保存原始数据，用于计算差异
			totalDays: 30,
			yearMonth: "----年--月",
			currentMonth: "", // 当前选中的月份
			userStats: [],
			canEdit: false, // 是否可以编辑（当月及之后可编辑）
			filledPlans: new Set(), // 已填写的计划，格式：userId_gridId_day
		};
	},

	mounted() {
		this.init();
	},

	methods: {
		getDay(day) {
			if (day < 10) {
				return "0" + day;
			} else {
				return day;
			}
		},

		checkDate(yearMonth) {
			this.currentMonth = yearMonth;
			this.getDetailByMonth(yearMonth);
		},

		async modalOpen() {
			try {
				this.modalShow = true;
				const res = await this.getFollowPlan();
				const res_selected = await this.getFollowPlanSelected();

				const checkList = res_selected.data.data;
				checkList.forEach((item) => {
					res.data.data.forEach((val) => {
						if (val.userId == item.id) {
							item.value = true;
							console.log(item.value, "item.value");
						}
					});
				});

				this.checkList = checkList;
				console.log(res.data.data, "res.data.data");
				console.log(checkList, "checkList");
			} catch (error) {
				console.log(error, "err");
			}
		},

		modalCancel() {
			this.modalShow = false;
			this.checkList = [];
		},

		modalConfirm() {
			console.log(this.checkList, "checkList");
			const userIds = [];
			this.checkList.forEach((item) => {
				if (item.value) userIds.push(item.id);
			});

			const postData = {
				userIds,
			};
			this.setFollowPlan(postData);
		},

		getFollowPlan() {
			const url = "/followPlan";
			return Util.request(url, {}, "get");
		},

		getFollowPlanSelected() {
			const url = "/rosterPersonnel/getList";
			return Util.request(url, { rosterType: 0 }, "post");
		},

		setFollowPlan(postData) {
			const url = "/followPlan/setPlan";
			console.log(postData, "postData");
			Util.request(url, postData, "post")
				.then((res) => {
					// console.log(res, "res");
					this.$Message.success("设置成功!");
					this.modalShow = false;
					this.checkDate(this.currentMonth);
				})
				.catch((err) => {
					console.log(err, "err");
				});
		},

		exportPlan() {
			const url = `/followPlan/export?yearMonth=${this.yearMonth}`;
			Util.request(url, {}, "get", {}, "blob")
				.then((resp) => {
					var blob = new Blob([resp.data], {
						type: "application/octet-stream",
					});
					var downloadElement = document.createElement("a");
					var href = window.URL.createObjectURL(blob);
					downloadElement.href = href;
					downloadElement.download = `${
						this.projectName
					}项目${this.getDate(this.yearMonth)}跟班计划.xls`;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				})
				.catch(function (error) {
					console.log("导出发生错误！", error);
				});
		},

		tooglePlan(val, day) {
			// 如果不可编辑，直接返回
			if (!this.canEdit) {
				this.$Message.warning("当月之前的计划不可编辑");
				return;
			}

			// 获取网格ID
			const gridId = this.getGridIdByUser(val);
			const planKey = `${val.userId}_${gridId}_${day}`;

			if (val.plan.includes(day)) {
				// 删除该计划 - 检查是否已填写
				if (this.filledPlans.has(planKey)) {
					this.$Message.warning("该计划已填写，不可删除");
					return;
				}
				const index = val.plan.indexOf(day);
				if (index !== -1) {
					val.plan.splice(index, 1);
				}
			} else {
				// 新增该计划
				val.plan.push(day);
			}
		},

		getRequirements() {
			const url = "/followPlan/requirements";
			Util.request(url, {}, "get")
				.then((res) => {
					// console.log(res, "res");
					this.workList = res.data.data.map((item) => item.content);
				})
				.catch((err) => {
					console.log(err, "err");
				});
		},

		getDate(yearMonth) {
			const year = moment(yearMonth).format("YYYY");
			const month = moment(yearMonth).format("MM");
			return `${year}年${month}月 `;
		},

		getMonths() {
			const url = "/followPlan/monthTree";
			Util.request(url, {}, "get")
				.then((res) => {
					this.dateList = res.data.data;
					// 只有当dateList不为空时才选择第一个月份
					if (this.dateList && this.dateList.length > 0) {
						const yearMonth = this.dateList[0].yearMonth;
						this.checkDate(yearMonth);
					}
				})
				.catch((err) => {
					console.log(err, "err");
				});
		},

		getDetailByMonth(month) {
			if (!month) return;
			const url = `/followPlan/planDetail/${month}`;
			Util.request(url, {}, "get")
				.then((res) => {
					// console.log(res, "res");
					const planDetail = res.data.data;
					const planArr = planDetail.gridInfos; // 网格点
					const userArr = planDetail.userPlanDetails; // 跟班人员

					// 重置已填写计划集合
					this.filledPlans.clear();

					planArr.forEach((item) => {
						item.arr = [];
						userArr.forEach((val) => {
							const obj = {
								name: val.userIdI18n,
								userId: val.userId, // 添加userId
								plan: [],
							};
							val.dayPlanStatuses.forEach((day) => {
								day.gridStatuses.forEach((el) => {
									if (el.gridId === item.gridId) {
										if (el.hasPlan) {
											obj.plan.push(day.day);
											// 如果已填写，添加到已填写集合中
											if (el.isFilled) {
												this.filledPlans.add(
													`${val.userId}_${item.gridId}_${day.day}`
												);
											}
										}
									}
								});
							});
							item.arr.push(obj);
						});
					});

					// 保存原始数据副本，用于计算差异
					this.originalPlanArr = JSON.parse(JSON.stringify(planArr));
					this.planArr = planArr;
					this.totalDays = planDetail.totalDays;
					this.yearMonth = planDetail.yearMonth;
					this.userStats = planDetail.userStats;

					// 判断是否可以编辑（当月及之后可编辑）
					this.canEdit = this.isCurrentOrFutureMonth(month);
				})
				.catch((err) => {
					console.log(err, "err");
				});
		},

		// 判断是否是当月或未来月份
		isCurrentOrFutureMonth(yearMonth) {
			const currentDate = new Date();
			const currentYearMonth = `${currentDate.getFullYear()}-${String(
				currentDate.getMonth() + 1
			).padStart(2, "0")}`;
			return yearMonth >= currentYearMonth;
		},

		// 根据用户获取网格ID
		getGridIdByUser(user) {
			for (let grid of this.planArr) {
				for (let u of grid.arr) {
					if (u.userId === user.userId) {
						return grid.gridId;
					}
				}
			}
			return null;
		},

		// 计算计划变更数据
		calculatePlanDifference() {
			const changes = [];

			// 收集所有用户ID
			const allUserIds = new Set();
			this.planArr.forEach((gridItem) => {
				gridItem.arr.forEach((userItem) => {
					allUserIds.add(userItem.userId);
				});
			});

			// 遍历每个用户的每一天
			allUserIds.forEach((userId) => {
				for (let day = 1; day <= this.totalDays; day++) {
					const currentGridIds = this.getUserGridIdsForDay(
						userId,
						day,
						this.planArr
					);
					const originalGridIds = this.getUserGridIdsForDay(
						userId,
						day,
						this.originalPlanArr
					);

					// 比较当前和原始数据
					if (!this.arraysEqual(currentGridIds, originalGridIds)) {
						changes.push({
							yearMonth: this.yearMonth,
							day: day,
							gridIds: currentGridIds,
							userId: userId,
						});
					}
				}
			});

			return changes;
		},

		// 获取某用户某天的网格ID列表
		getUserGridIdsForDay(userId, day, planArr) {
			const gridIds = [];
			planArr.forEach((gridItem) => {
				const userItem = gridItem.arr.find((u) => u.userId === userId);
				if (userItem && userItem.plan.includes(day)) {
					gridIds.push(gridItem.gridId);
				}
			});
			return gridIds.sort(); // 排序确保比较一致性
		},

		// 比较两个数组是否相等
		arraysEqual(arr1, arr2) {
			if (arr1.length !== arr2.length) return false;
			const sorted1 = [...arr1].sort();
			const sorted2 = [...arr2].sort();
			return sorted1.every((val, index) => val === sorted2[index]);
		},

		// 保存计划
		savePlan() {
			const changes = this.calculatePlanDifference();

			// 如果没有变化，提示用户
			if (changes.length === 0) {
				this.$Message.info("没有变化需要保存");
				return;
			}

			console.log("保存数据:", changes);

			Util.request("/followPlan/modifyPlan", changes, "put")
				.then((res) => {
					if (res.data.success) {
						this.$Message.success("保存成功!");
						// 重新获取数据刷新页面
						this.getDetailByMonth(this.currentMonth);
					} else {
						this.$Message.error(res.data.message || "保存失败");
					}
				})
				.catch((err) => {
					console.error("保存失败:", err);
					this.$Message.error("保存失败，请重试");
				});
		},

		init() {
			this.getRequirements();
			this.getMonths();
		},
	},
};
</script>

<style lang="less" scoped>
@import "../style/Plan.less";

.plan {
	.Plan();
}
</style>
