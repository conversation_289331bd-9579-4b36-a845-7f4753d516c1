
  <template>
    <div>
      <Tabs v-model="tabValue">
        <TabPane label="监督组织" name="监督组织"></TabPane>
        <TabPane label="网格现场公示牌" name="网格现场公示牌"></TabPane>
      </Tabs>
      <div class="org-container" @wheel="handleWheel" @dblclick="resetScale" @mousedown="startDrag" @mousemove="onDrag" @mouseup="endDrag" @mouseleave="endDrag" ref="containerRef" v-if="tabValue == '监督组织'">
        <OrganizationTree :json="data" :class="{landscape: landscape.length}" @click-node="clickNode" :style="{transform: `scale(${scale}) translate(${translateX}px, ${translateY}px)`, cursor: isDragging ? 'grabbing' : 'grab', transition: isDragging ? 'none' : 'transform 0.3s ease'}" />
        <!-- 缩放比例显示 -->
        <div class="scale-indicator">
          {{ Math.round(scale * 100) }}%
        </div>
        <!-- 位置指示器 -->
        <div class="position-indicator">
          X: {{ Math.round(translateX) }}, Y: {{ Math.round(translateY) }}
        </div>
        <!-- 操作提示 -->
        <div class="help-indicator">
          🖱️ 滚轮缩放 | 拖拽移动 | 双击重置 | 空格键重置
        </div>
        <Modal v-model="showUpdateModal" title="管理职责"> 
          <span>
            1根据项目特点、结合施工阶段,以施工现场生产组织单元为基础,合理划分网格配置相应人员进行安全监督管理。<br />
            2成立安监办,负责对各业务部门和网格安全员的安全覆职行为进行监督。<br />
            3 制定并落实检查制度,明确检查频次,实现管理人员检查定量化。<br />
            4监督跟班作业制度、班前讲话制度、作业人员进场审核机制、关键施工作业安全条件确认制度、安全教育培训制度等制定及其落实情况。<br />
            5建立穿透式管理考核制度,明确对网格安全员、管理人员监督检查情况等方面的考核,严格督促各级管理人员履职。<br />
            6制定施工项目部与劳务班组一致认可的安全操作规程与奖罚条例等规则,作为网格内劳务班组安全管理的约束指标和考核依据。<br />
            7按照财政部、应急管理部《企业安全生产费用提取和使用管理办法》规定,合理使用企业安全生产费,为落实安全穿透式管理提供重要保障。<br />
          </span>
          
          <div slot="footer">
            <Button @click="showUpdateModal = false">取消</Button>
          </div>
        </Modal>
        <Modal v-model="showPersonInfo" title="详情" width="800px" :footer-hide="true" :closable="true">
          <!-- 在这里写 -->
          <div class="person-info-modal">
            <!-- 基础信息 -->
            <div class="info-section">
              <div class="section-header">
                <Icon type="md-document" color="#4A90E2" size="20" />
                <span class="section-title">基础信息</span>
              </div>
              
              <div class="person-detail">
                <div class="avatar-section">
                  <img :src="selectedPersonInfo.profilePict || require('../../../images/state/empty.png')" alt="头像" class="person-avatar" />
                </div>
                
                <div class="info-content">
                  <div class="left-info">
                    <div class="info-item">
                      <span class="label">安监办职务：</span>
                      <span class="value">{{ getDisplayOfficePosition() }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">联系电话：</span>
                      <span class="value">{{ selectedPersonInfo.linkPhone || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">安全教育：</span>
                      <span class="value">{{ selectedPersonInfo.safetyEduRes || '-' }}</span>
                    </div>
                  </div>
                  
                  <div class="right-info">
                    <div class="info-item">
                      <span class="label">姓名：</span>
                      <span class="value">{{ selectedPersonInfo.name || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">岗位：</span>
                      <span class="value">{{ selectedPersonInfo.post || '-' }}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 附件信息 -->
              <div class="attachments-section" v-if="selectedPersonInfo.socialBookContent || selectedPersonInfo.appointBookContent || getAuthBookList().length > 0">
                <div class="attachment-row" v-if="selectedPersonInfo.socialBookContent || selectedPersonInfo.appointBookContent">
                  <div class="attachment-group" v-if="selectedPersonInfo.socialBookContent">
                    <span class="attachment-label">社保证明：</span>
                    <div class="attachment-files">
                      <div class="attachment-file" 
                           v-for="(fileName, index) in getSocialBookFiles()" 
                           :key="'social-' + index"
                           @click="previewFile(fileName)">
                        <Icon type="ios-document" size="24" color="#CCCCCC" />
                        <span>{{ getFileNameFromUrl(fileName) }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="attachment-group" v-if="selectedPersonInfo.appointBookContent">
                    <span class="attachment-label">任命书：</span>
                    <div class="attachment-files">
                      <div class="attachment-file" 
                           v-for="(fileName, index) in getAppointBookFiles()" 
                           :key="'appoint-' + index"
                           @click="previewFile(fileName)">
                        <Icon type="ios-document" size="24" color="#CCCCCC" />
                        <span>{{ getFileNameFromUrl(fileName) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 证书信息 -->
                <div class="attachment-row" v-if="getAuthBookList().length > 0">
                  <div class="attachment-group full-width">
                    <span class="attachment-label">证书：</span>
                    <div class="attachment-files">
                      <div class="attachment-file" 
                           v-for="(cert, index) in getAuthBookList()" 
                           :key="'cert-' + index"
                           @click="previewFile(cert.authBookContent || cert.url || cert.path || cert)">
                        <Icon type="ios-document" size="24" color="#CCCCCC" />
                        <span>{{ getAuthBookFileName(cert, index) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 岗位职责 -->
            <div class="info-section" v-if="getWorkResponsibilities().length > 0">
              <div class="section-header">
                <Icon type="md-document" color="#4A90E2" size="20" />
                <span class="section-title">岗位职责</span>
              </div>
              
              <div class="responsibilities-content">
                <p class="responsibilities-title">对网格的监督职责：</p>
                <div class="responsibilities-list">
                  <div class="responsibility-item" v-for="(item, index) in getWorkResponsibilities()" :key="index">
                    <span class="number">{{ index + 1 }}.</span>
                    <span class="text">{{ item }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div slot="footer">
            <Button @click="showPersonInfo = false">取消</Button>
            <Button type="primary" @click="ok">确认</Button>
          </div>
        </Modal>
        
        <!-- 部门职责弹框 -->
        <Modal v-model="showOrgResponsibilityModal" title="部门职责" width="800px" :footer-hide="true" :closable="true">
          <div class="org-responsibility-modal">
            <div class="responsibility-section">
              <div class="section-header">
                <Icon type="md-document" color="#4A90E2" size="20" />
                <span class="section-title">{{ currentOrgName || '部门职责列表' }}</span>
              </div>
              
              <div class="responsibility-content">
                <div class="responsibility-list">
                  <div class="responsibility-item" v-for="(item, index) in orgResponsibilityData" :key="index">
                    <span class="number">{{ index + 1 }}.</span>
                    <span class="text">{{ item.responsibility || item.content || item.description || item }}</span>
                  </div>
                  <div v-if="orgResponsibilityData.length === 0" class="no-data">
                    暂无职责信息
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Modal>
        <!-- 图片预览弹框 -->
        <Modal v-model="showImagePreview" title="图片预览" width="800px" :footer-hide="true" :closable="true">
          <div class="image-preview-modal">
            <div class="image-container">
              <img :src="previewImageUrl" alt="预览图片" class="preview-image" @load="onImageLoad" @error="onImageError" />
              <div v-if="imageLoading" class="loading-indicator">
                <Spin size="large"></Spin>
                <p>图片加载中...</p>
              </div>
              <div v-if="imageError" class="error-indicator">
                <Icon type="ios-alert" size="48" color="#ff6b6b" />
                <p>图片加载失败</p>
                <p class="error-url">{{ previewImageUrl }}</p>
              </div>
            </div>
            <div class="image-info">
              <p class="image-name">{{ previewImageName }}</p>
            </div>
          </div>
        </Modal>
      </div>
      <div v-if="tabValue == '网格现场公示牌'">
        <NetworkNoticeBoard />
      </div>
    </div>
  </template>

<script>
  import OrganizationTree from '@/components/OrganizationTree.vue';
  import NetworkNoticeBoard from '@/views/networkNoticeBoard/index.vue';
  import { superviseList } from './index';
  import Util from '@/libs/util';
  export default {
    components: {
      OrganizationTree,
      NetworkNoticeBoard
    },
    data() {
      return {
        landscape: [],
        data: {},
        scale: 0.4, // 初始缩放比例
        defaultScale: 0.4, // 默认缩放比例
        minScale: 0.3, // 最小缩放比例
        maxScale: 2.0, // 最大缩放比例
        scaleStep: 0.01, // 缩放步长
        // 拖拽相关状态
        isDragging: false,
        translateX: 0,
        translateY: 0,
        lastMouseX: 0,
        lastMouseY: 0,
        startX: 0,
        startY: 0,
        // 键盘状态
        isSpacePressed: false,
        isCtrlPressed: false,
        // 边界限制
        maxTranslateX: 5000,
        maxTranslateY: 5000,
        showUpdateModal: false,
        tabValue: '监督组织',
        showPersonInfo: false,
        selectedPersonInfo: {},
        // 部门职责弹框相关
        showOrgResponsibilityModal: false,
        orgResponsibilityData: [],
        currentOrgName: '', // 当前选中的部门名称
        // 图片预览弹框相关
        showImagePreview: false,
        previewImageUrl: '',
        previewImageName: '',
        imageLoading: true,
        imageError: false
      }
    },
    mounted() {
      this.initRequest();
      // 添加键盘事件监听
      document.addEventListener('keydown', this.onKeyDown);
      document.addEventListener('keyup', this.onKeyUp);
    },
    beforeDestroy() {
      // 移除键盘事件监听
      document.removeEventListener('keydown', this.onKeyDown);
      document.removeEventListener('keyup', this.onKeyUp);
    },
    methods: {
      initRequest() {
        // 获取当前项目名称
        Util.request("/index/project").then((resp) => {
          const projectName = resp.data.data.platformProjectName;
          
          // 获取监督组织结构数据
          Util.request('/gridManagementInfo/supervisoryOrgStructureChart').then(res => {
            let levelOne = ['安监办主任', '安监办常务副主任', '安监办副主任'];
            
            // 映射levelOne
            levelOne = levelOne.map(i => {
              let item = res.data.data.levelOne.find(j => j && j.officePosition == i);
              if (item) {
                return {...item, type: 'card'};
              } 
              return {
                type: 'none',
                officePosition: i,
                detail: '待填写'
              }
            })

            let levelTwo = res.data.data.levelTwo;

            // 安监专务待填写
            let levelFour = [];
            let levelFourItem = [];

            if (res.data.data.levelFour.length != 0) {
              res.data.data.levelFour.forEach(i => {
                levelFourItem.push(i);
              })
              levelFour.push({
                type: 'AJNone',
                officePosition: '安监专务',
                realName: '未填写',
                levelThree: levelFourItem
              })
            }

            // 初始化
            if (levelTwo.length == 0 && !levelFour[0].levelThree[0].tens) {
              console.log('初始化')
              levelFour[0].levelThree[0].type = 'notBox';
            }
            
            let orgs = res.data.data.orgs;
            let Meun = this.createNestedMenu(levelOne, [...levelTwo, ...levelFour]);
            let orgsChildren = this.orgsMenu(orgs);
            Meun[0].children.unshift(orgsChildren);
            this.data = {
              type: 'title',
              name: projectName, // 使用动态获取的项目名称
              image_url: "https://picsum.photos/800/600?201",
              children: Meun
            }
          })
        })
      },
      // 创建安全管理部门菜单
      orgsMenu(orgs) {
        return {
          type: 'group',
          name: 'group',
          orgList: orgs, // 保存完整的部门列表，包含每个部门的ID
          items: orgs.map(i => i.name),
          children: [
            {
              type: 'item',
              name: '施工作业班组'
            }
          ]
        }
      },
      // 创建第一层菜单
      createNestedMenu(titles, levelTwo, level = 0) {
        if (level >= titles.length) return null;
        let obj = {};
        if (titles[level].type == 'card') {
          obj = {
            id: titles[level].cpId,
            type: titles[level].type,
            name: titles[level].officePosition ,
            image_url: titles[level].photo || '', 
            user: titles[level].realName || titles[level].detail,
            position: titles[level].post,
            phone: titles[level].linkPhone,
          }
        }
        if (titles[level].type == 'none') {
          obj = {
            name: titles[level].officePosition,
            type: titles[level].type,
            detail: titles[level].detail,
          }
        }
        return [{...obj, children: this.createNestedMenu(titles, levelTwo, level + 1) || this.lastMenu(levelTwo)}];
      },
      // 创建最后一层菜单
      lastMenu(levelTwo) {
        return levelTwo.map(i => {
          console.log('处理levelTwo人员:', i.realName, 'cpId:', i.cpId);
          let option = {};
          
          option.name = i.officePosition;
          option.image_url = i.photo || '';
          option.user = i.realName;
          option.detail = i.realName;
          option.position = i.post;
          option.phone = i.linkPhone;
          option.type = i.type || 'card';
          option.id = i.cpId;
          if (i.levelThree) {
            option.children = i.levelThree.map(i => {
              let option1 = {};
              if (i.type == 'notBox') {
                option1.type = i.type,
                option1.name = '待填写'
                return option1;
              }
              // 11 必须有
              option1.gridId = i.gridId;
              option1.name = i.gridAndMileage || '11';
              option1.type = 'text';
              option1.text = i.gridAndMileage || ''
              if (i.tens) {
                option1.children = [
                  {
                    id: i.cpId,
                    type: 'person',
                    name: 'empty',
                    // gridId: i.gridId,
                    dataList: i.tens.map(j => {
                      console.log('处理人员数据:', j.realName, 'cpId:', j.cpId);
                      return {
                        id: j.cpId,
                        gridId: i.gridId,
                        name: j.realName,
                        user: j.realName,
                        position: j.post,
                        phone: j.linkPhone,
                        image_url: j.photo || ''
                      }
                    })
                  }
                ]
              }
              return option1;
            })
          }
          return option;
        })
      },
       // 处理鼠标滚轮事件
       handleWheel(event) {
         event.preventDefault(); // 阻止默认滚动行为
         
         // 获取滚轮方向
         const delta = event.deltaY;
         
         if (delta > 0) {
           // 向下滚动，缩小
           this.scale = Math.max(this.minScale, this.scale - this.scaleStep);
         } else {
           // 向上滚动，放大
           this.scale = Math.min(this.maxScale, this.scale + this.scaleStep);
         }
         
         // 保留两位小数
         this.scale = Math.round(this.scale * 100) / 100;
       },
       // 重置缩放比例和位置
       resetScale() {
         this.scale = this.defaultScale;
         this.translateX = 0;
         this.translateY = 0;
       },
       // 开始拖拽
       startDrag(event) {
         // 只响应鼠标左键
         if (event.button !== 0) return;
         
         this.isDragging = true;
         this.lastMouseX = event.clientX;
         this.lastMouseY = event.clientY;
         this.startX = event.clientX;
         this.startY = event.clientY;
         
         // 阻止默认行为
         event.preventDefault();
       },
       // 拖拽过程中
       onDrag(event) {
         if (!this.isDragging) return;
         
         const deltaX = event.clientX - this.lastMouseX;
         const deltaY = event.clientY - this.lastMouseY;
         
         // 计算新的位置
         const newX = this.translateX + deltaX;
         const newY = this.translateY + deltaY;
         
         // 应用边界限制
         this.translateX = Math.max(-this.maxTranslateX, Math.min(this.maxTranslateX, newX));
         this.translateY = Math.max(-this.maxTranslateY, Math.min(this.maxTranslateY, newY));
         
         this.lastMouseX = event.clientX;
         this.lastMouseY = event.clientY;
         
         // 阻止默认行为
         event.preventDefault();
       },
       // 结束拖拽
       endDrag(event) {
         if (!this.isDragging) return;
         
         this.isDragging = false;
         
         // 计算拖拽距离，如果距离很小，认为是点击而不是拖拽
         const dragDistance = Math.sqrt(
           Math.pow(event.clientX - this.startX, 2) + 
           Math.pow(event.clientY - this.startY, 2)
         );
         
         // 如果拖拽距离小于5像素，认为是点击事件
         if (dragDistance < 5) {
           // 这里可以处理点击事件
         }
       },
       // 键盘按下事件
       onKeyDown(event) {
         if (event.code === 'Space') {
           this.isSpacePressed = true;
           // 按空格键重置位置和缩放
           this.resetScale();
           event.preventDefault();
         } else if (event.code === 'ControlLeft' || event.code === 'ControlRight') {
           this.isCtrlPressed = true;
         }
       },
       // 键盘松开事件
       onKeyUp(event) {
         if (event.code === 'Space') {
           this.isSpacePressed = false;
         } else if (event.code === 'ControlLeft' || event.code === 'ControlRight') {
           this.isCtrlPressed = false;
         }
       },
       clickNode(node) {
        console.log(node, 'clickNode - 节点信息');
        console.log('节点ID:', node.id, '节点类型:', node.type);

          if (node.type == 'none') {
            // 待处理
            this.$router.push('/manage/system/roster');
          } else if (node.type == 'group') {
            // 处理部门职责点击
            this.getOrgResponsibility(node);
          } else if (node.type == 'title') {
            this.showUpdateModal = true;
          } else if (node.type == 'text') {
            console.log('点击的文字') ;
          } else if (node.type == 'AJNone') {
            console.log('点击的安监专务')
          } else {
            let data = '&gridId=' + node.gridId;
            // 检查节点是否有有效的ID
            if (!node.id) {
              console.error('节点ID为空，无法获取人员详情:', node);
              this.$Message.error('无法获取人员详情，缺少人员ID');
              return;
            }
            if (!node.gridId) {
              data = '';
            }
            
            // 所有人员都调用API获取详细信息
            console.log('发送请求获取人员详情，ID:', node.id);
            Util.requestGet('/gridManagementInfo/getGridStaff?rostId=' + node.id + data).then(res => {
              console.log('人员详情API响应:', res);
              if (res.data.code  == 'success') {
                this.selectedPersonInfo = res.data.data;
                this.showPersonInfo = true;
              } else {
                this.$Message.error('获取人员详情失败: ' + (res.data.message || '未知错误'));
              }
            }).catch(err => {
              console.error('获取人员详情请求失败:', err);
              this.$Message.error('获取人员详情请求失败');
            });
          }
       },
       
       // 获取部门职责
       getOrgResponsibility(node) {
         let orgId = "dd289b1b3668410ea4a33563561c1b27"; // 默认ID
         
         // 如果是从组件传来的单个部门点击事件
         if (node.index !== undefined && node.orgList && node.orgList[node.index]) {
           orgId = node.orgList[node.index].id;
         }
         
         Util.request('/orgResponsibility/list', {
           orgId: orgId
         }, 'POST').then(res => {
           if (res.data.code == 'success') {
             this.orgResponsibilityData = res.data.data || [];
             this.currentOrgName = node.name; // 设置当前选中的部门名称
             this.showOrgResponsibilityModal = true;
           } else {
             this.$Message.error('获取部门职责失败');
           }
         }).catch(err => {
           this.$Message.error('获取部门职责失败');
           console.error(err);
         });
       },
       previewFile(file) {
         // 重置状态
         this.imageLoading = true;
         this.imageError = false;
         
         // 设置预览图片信息
         this.previewImageUrl = file;
         this.previewImageName = this.getFileNameFromUrl(file);
         this.showImagePreview = true;
       },
       ok() {
         this.showPersonInfo = false;
       },
       getSocialBookFiles() {
         if (this.selectedPersonInfo.socialBookContent) {
           // 如果是单个URL，直接返回数组  
           if (typeof this.selectedPersonInfo.socialBookContent === 'string') {
             // 检查是否包含逗号，如果包含则按逗号分割，否则作为单个文件处理
             if (this.selectedPersonInfo.socialBookContent.includes(',')) {
               return this.selectedPersonInfo.socialBookContent.split(',').map(f => f.trim());
             } else {
               return [this.selectedPersonInfo.socialBookContent];
             }
           }
         }
         return [];
       },
       getAppointBookFiles() {
         if (this.selectedPersonInfo.appointBookContent) {
           // 如果是单个URL，直接返回数组
           if (typeof this.selectedPersonInfo.appointBookContent === 'string') {
             // 检查是否包含逗号，如果包含则按逗号分割，否则作为单个文件处理
             if (this.selectedPersonInfo.appointBookContent.includes(',')) {
               return this.selectedPersonInfo.appointBookContent.split(',').map(f => f.trim());
             } else {
               return [this.selectedPersonInfo.appointBookContent];
             }
           }
         }
         return [];
       },
       getWorkResponsibilities() {
         // 如果workResponsibility是数组且有内容
         if (this.selectedPersonInfo.workResponsibility && Array.isArray(this.selectedPersonInfo.workResponsibility) && this.selectedPersonInfo.workResponsibility.length > 0) {
           return this.selectedPersonInfo.workResponsibility.filter(item => item.content).map(item => {
              return item.content;
           });
         }
         return [];
       },
       getAuthBookList() {
         // 处理证书JSON字符串
         if (this.selectedPersonInfo.authBookContent) {
           try {
             // 如果是字符串，尝试解析为JSON
             if (typeof this.selectedPersonInfo.authBookContent === 'string') {
               const authBooks = JSON.parse(this.selectedPersonInfo.authBookContent);
               return Array.isArray(authBooks) ? authBooks : [];
             }
             // 如果已经是数组，直接返回
             if (Array.isArray(this.selectedPersonInfo.authBookContent)) {
               return this.selectedPersonInfo.authBookContent;
             }
           } catch (error) {
             console.error('解析证书数据失败:', error);
             return [];
           }
         }
         return [];
       },
       getAuthBookFileName(cert, index) {
         if (cert.name) {
           return cert.name;
         }
         if (cert.fileName) {
           return cert.fileName;
         }
         return '证书文件' + (index + 1);
       },
       onImageLoad() {
         this.imageLoading = false;
         this.imageError = false;
       },
       onImageError() {
         this.imageLoading = false;
         this.imageError = true;
       },
       getFileNameFromUrl(url) {
         if (!url) return '未知文件';
         try {
           // 从URL中提取文件名
           const fileName = url.split('/').pop();
           // 解码文件名（处理中文等特殊字符）
           return decodeURIComponent(fileName);
         } catch (error) {
           return '文件';
         }
       },
       getDisplayOfficePosition() {
         const officePositions = ['安监办主任', '安监办常务副主任', '安监办副主任'];
         if (officePositions.includes(this.selectedPersonInfo.officePosition)) {
           return this.selectedPersonInfo.officePosition;
         }
         return '--';
       }
    }
  }
</script>

    <style lang="less" scoped>
    .org-container {
      width: 100%;
      height: calc(~'100vh - 145px');
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: auto;
      background: #f5f5f5; /* 背景色 */
      padding: 20px; /* 内边距 */
      box-sizing: border-box;
      /* 隐藏滚动条但保持滚动功能 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE和Edge */
    }
    
    /* 隐藏Webkit浏览器的滚动条 */
    .org-container::-webkit-scrollbar {
      display: none;
    }
    
    .scale-indicator {
      position: absolute;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      z-index: 1000;
      pointer-events: none;
      user-select: none;
    }
    
    .position-indicator {
      position: absolute;
      top: 40px;
      right: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      z-index: 1000;
      pointer-events: none;
      user-select: none;
    }
    
    .help-indicator {
      position: absolute;
      bottom: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 1000;
      pointer-events: none;
      user-select: none;
    }
    
    /* 禁用文本选择和拖拽 */
    * {
      user-select: none;
      -webkit-user-drag: none;
    }

    /* 基础信息弹框样式 */
    .person-info-modal {
      padding: 20px;
      background: #f5f5f5;
    }
    
    .person-info-modal .info-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .person-info-modal .info-section .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .person-info-modal .info-section .section-header .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-left: 8px;
    }
    
    .person-info-modal .info-section .person-detail {
      display: flex;
      gap: 30px;
      align-items: flex-start;
    }
    
    .person-info-modal .info-section .person-detail .avatar-section .person-avatar {
      width: 120px;
      height: 120px;
      border-radius: 8px;
      object-fit: cover;
      background: #f0f0f0;
      border: 1px solid #e0e0e0;
    }
    
    .person-info-modal .info-section .person-detail .info-content {
      display: flex;
      justify-content: space-between;
      gap: 30px;
      flex: 1;
    }

    .person-info-modal .info-section .person-detail .info-content .left-info,
    .person-info-modal .info-section .person-detail .info-content .right-info {
      flex: 1;
    }
    
    .person-info-modal .info-section .person-detail .info-content .left-info .info-item,
    .person-info-modal .info-section .person-detail .info-content .right-info .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .person-info-modal .info-section .person-detail .info-content .left-info .info-item .label,
    .person-info-modal .info-section .person-detail .info-content .right-info .info-item .label {
      color: #666;
      font-size: 14px;
      margin-right: 8px;
      white-space: nowrap;
      min-width: 80px;
    }
    
    .person-info-modal .info-section .person-detail .info-content .left-info .info-item .value,
    .person-info-modal .info-section .person-detail .info-content .right-info .info-item .value {
      color: #333;
      font-size: 14px;
      font-weight: 500;
    }
    
    .person-info-modal .info-section .attachments-section {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
    }

    .person-info-modal .info-section .attachments-section .attachment-row {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      margin-bottom: 15px;
    }

    .person-info-modal .info-section .attachments-section .attachment-group {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .person-info-modal .info-section .attachments-section .attachment-group.full-width {
      flex: none;
      width: 48%;
    }

    .person-info-modal .info-section .attachments-section .attachment-label {
      color: #666;
      font-size: 14px;
      min-width: 80px;
      text-align: left;
      white-space: nowrap;
    }
    
    .person-info-modal .info-section .attachments-section .attachment-files {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .person-info-modal .info-section .attachments-section .attachment-file {
      display: flex;
      align-items: center;
      background: #f8f8f8;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      padding: 10px 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      flex: 1;
      min-height: 40px;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .person-info-modal .info-section .attachments-section .attachment-file span {
      color: #333;
      font-size: 13px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
    }
    
    .person-info-modal .info-section .responsibilities-content .responsibilities-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 16px;
      font-weight: 400;
    }
    
    .person-info-modal .info-section .responsibilities-content .responsibilities-list .responsibility-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      padding: 8px 0;
    }
    
    .person-info-modal .info-section .responsibilities-content .responsibilities-list .responsibility-item .number {
      color: #333;
      font-size: 14px;
      margin-right: 8px;
      font-weight: 400;
      min-width: 20px;
    }
    
    .person-info-modal .info-section .responsibilities-content .responsibilities-list .responsibility-item .text {
      color: #333;
      font-size: 14px;
      line-height: 1.6;
      flex: 1;
    }
    
    .person-info-modal .info-section .responsibilities-content .responsibilities-list .no-data {
      text-align: center;
      color: #999;
      font-size: 14px;
      padding: 40px 0;
      background: #f8f9fa;
      border-radius: 6px;
    }
    /* 部门职责弹框样式 */
    .org-responsibility-modal {
      padding: 20px;
      background: #f5f5f5;
    }
    
    .org-responsibility-modal .responsibility-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
    }
    
    .org-responsibility-modal .responsibility-section .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .org-responsibility-modal .responsibility-section .section-header .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-left: 8px;
    }
    
    .org-responsibility-modal .responsibility-section .responsibility-content .responsibility-list .responsibility-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #4A90E2;
    }
    
    .org-responsibility-modal .responsibility-section .responsibility-content .responsibility-list .responsibility-item .number {
      color: #4A90E2;
      font-size: 14px;
      margin-right: 12px;
      font-weight: 600;
      min-width: 24px;
    }
    
    .org-responsibility-modal .responsibility-section .responsibility-content .responsibility-list .responsibility-item .text {
      color: #333;
      font-size: 14px;
      line-height: 1.6;
      flex: 1;
    }
    
    .org-responsibility-modal .responsibility-section .responsibility-content .responsibility-list .no-data {
      text-align: center;
      color: #999;
      font-size: 14px;
      padding: 40px 0;
      background: #f8f9fa;
      border-radius: 6px;
    }
    /* 图片预览弹框样式 */
    .image-preview-modal {
      padding: 20px;
      background: #f5f5f5;
    }

    .image-preview-modal .image-container {
      position: relative;
      width: 100%;
      height: 600px; /* 固定高度 */
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #e0e0e0;
    }

    .image-preview-modal .image-container .preview-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      display: block;
    }

    .image-preview-modal .image-container .loading-indicator,
    .image-preview-modal .image-container .error-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #666;
      font-size: 16px;
      z-index: 10;
    }

    .image-preview-modal .image-container .error-indicator {
      color: #ff6b6b;
    }

    .image-preview-modal .image-container .error-indicator .error-url {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
      word-break: break-all;
    }
     
     .image-preview-modal .image-info {
       margin-top: 15px;
       padding: 10px;
       background: #fff;
       border-radius: 6px;
       border: 1px solid #e0e0e0;
     }
     
     .image-preview-modal .image-info .image-name {
       margin: 0;
       font-size: 14px;
       color: #333;
       font-weight: 500;
     }
    </style>
